# YCA Collector Development Guide

## Quick Start

### Initial Setup
```bash
cd backend/
./scripts/dev-setup.sh
```

### Daily Development Commands
```bash
# Start development server
./scripts/run-dev.sh

# Run code quality checks
./scripts/quality-check.sh

# Install new dependency
uv add package-name

# Install dev dependency
uv add --dev package-name

# Update dependencies
uv sync --upgrade
```

## Code Quality

### Automated Checks (Pre-commit)
- Ruff formatting and linting
- Pyright type checking
- Basic file checks (trailing whitespace, etc.)
- Test execution

### Manual Quality Checks
```bash
# Format code
uv run ruff format app/

# Lint code
uv run ruff check app/ --fix

# Type check
uv run pyright app/

# Run tests
uv run pytest

# Run tests with coverage
uv run pytest --cov=app
```

## Development Workflow

1. **Install dependencies**: `uv sync`
2. **Start coding**: Edit files in `app/`
3. **Run quality checks**: `./scripts/quality-check.sh`
4. **Test your changes**: `uv run pytest`
5. **Commit**: Pre-commit hooks run automatically

## File Structure

```
backend/
├── app/                 # Main application code
├── tests/              # Test files
├── scripts/            # Development scripts
├── pyproject.toml      # Project configuration
├── uv.lock            # Locked dependencies
└── .env               # Environment variables
```

## Configuration Files

- `pyproject.toml`: Project metadata, dependencies, tool configurations
- `.pre-commit-config.yaml`: Pre-commit hooks configuration
- `uv.lock`: Locked dependency versions for reproducible builds

## Environment Variables

Copy `.env.example` to `.env` and configure:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key
- `GEMINI_API_KEY`: Google Gemini API key

## Testing

```bash
# Run all tests
uv run pytest

# Run specific test file
uv run pytest tests/test_example.py

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run tests matching pattern
uv run pytest -k "test_pattern"
```

## Troubleshooting

### Dependency Issues
```bash
# Reset virtual environment
rm -rf .venv/
uv sync

# Update lock file
uv lock --upgrade
```

### Pre-commit Issues
```bash
# Ensure pre-commit is available
uv add --dev pre-commit

# Install Git hook scripts
uv run pre-commit install

# Run all hooks against all files
uv run pre-commit run --all-files
```

### Type Check Errors
- Ensure all functions have type hints
- Use `Optional[Type]` for nullable values
- Add `# type: ignore` for unavoidable issues
