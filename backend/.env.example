# ================================
# YCA Collector Environment Configuration
# ================================
# Copy this file to .env and update with your actual values
# DO NOT commit the .env file to version control

# ================================
# Application Settings
# ================================
APP_ENV=development
APP_SECRET_KEY=change-this-to-a-secure-random-string-in-production
APP_DEBUG=true

# ================================
# Server Configuration
# ================================
HOST=0.0.0.0
PORT=8000

# ================================
# Supabase Configuration
# ================================
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# ================================
# Google AI (Gemini) Configuration
# ================================
# Get your API key from https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# ================================
# Storage Configuration
# ================================
STORAGE_BUCKET=creative-collector
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes

# ================================
# CORS Configuration
# ================================
# Add your frontend URLs here
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:3000"]

# ================================
# Logging Configuration
# ================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ================================
# Development Settings (Optional)
# ================================
# RELOAD_ON_CHANGE=true
# ENABLE_DOCS=true
