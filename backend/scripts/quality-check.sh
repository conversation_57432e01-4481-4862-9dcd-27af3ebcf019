#!/bin/bash

# YCA Collector Code Quality Check Script
# Runs all code quality tools and reports results

set -e

echo "🔍 Running comprehensive code quality checks..."

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: Please run this script from the backend/ directory"
    exit 1
fi

# Initialize counters
FAILED_CHECKS=0
TOTAL_CHECKS=5

echo ""
echo "1️⃣  Running Ruff linting..."
if uv run ruff check app/; then
    echo "✅ Ruff linting passed"
else
    echo "❌ Ruff linting failed"
    ((FAILED_CHECKS++))
fi

echo ""
echo "2️⃣  Running Ruff formatting check..."
if uv run ruff format --check app/; then
    echo "✅ Ruff formatting passed"
else
    echo "❌ Ruff formatting failed (run: uv run ruff format app/)"
    ((FAILED_CHECKS++))
fi

echo ""
echo "3️⃣  Running Pyright type checking..."
if uv run pyright app/; then
    echo "✅ Pyright type checking passed"
else
    echo "❌ Pyright type checking failed"
    ((FAILED_CHECKS++))
fi

echo ""
echo "4️⃣  Running tests..."
if uv run pytest tests/ --tb=short; then
    echo "✅ Tests passed"
else
    echo "❌ Tests failed"
    ((FAILED_CHECKS++))
fi

echo ""
echo "5️⃣  Running tests with coverage..."
if uv run pytest tests/ --cov=app --cov-report=term-missing; then
    echo "✅ Tests with coverage passed"
else
    echo "❌ Tests with coverage failed"
    ((FAILED_CHECKS++))
fi

echo ""
echo "📊 Quality Check Summary:"
echo "===================="
PASSED_CHECKS=$((TOTAL_CHECKS - FAILED_CHECKS))
echo "✅ Passed: $PASSED_CHECKS/$TOTAL_CHECKS"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo "🎉 All quality checks passed! Your code is ready."
    exit 0
else
    echo "❌ Failed: $FAILED_CHECKS/$TOTAL_CHECKS"
    echo "🔧 Please fix the failing checks before committing."
    exit 1
fi
