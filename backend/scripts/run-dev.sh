#!/bin/bash

# YCA Collector Development Server Script
# Starts the development server with optimal settings

set -e

echo "🚀 Starting YCA Collector development server..."

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: Please run this script from the backend/ directory"
    exit 1
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  Warning: .env file not found"
    echo "💡 Tip: Copy .env.example to .env and configure your settings"
fi

# Check if dependencies are installed
if [ ! -d ".venv" ]; then
    echo "📦 Virtual environment not found. Running uv sync..."
    uv sync
fi

echo "🌐 Starting FastAPI development server..."
echo "📍 API Documentation: http://localhost:8000/docs"
echo "📍 Health Check: http://localhost:8000/health"
echo "🔄 Auto-reload enabled"
echo ""
echo "Press Ctrl+C to stop the server"

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
