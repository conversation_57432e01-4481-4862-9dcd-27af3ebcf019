#!/bin/bash

# YCA Collector Development Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up YCA Collector development environment..."

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: Please run this script from the backend/ directory"
    exit 1
fi

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ Error: uv is not installed. Please install it first:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "📦 Installing dependencies with uv..."
uv sync

echo "🔧 Setting up pre-commit hooks..."
uv run pre-commit install

echo "📁 Creating necessary directories..."
mkdir -p uploads
mkdir -p logs

echo "🔧 Setting up environment file..."
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 Created .env from .env.example"
        echo "🔑 Please edit .env with your actual API keys and configuration"
    else
        echo "⚠️  No .env.example found. Please create .env manually"
    fi
else
    echo "✅ .env already exists"
fi

echo "🧪 Running initial code quality checks..."
uv run ruff check --fix app/
uv run ruff format app/
uv run pyright app/

echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "  1. Edit .env with your API keys (Supabase, Gemini, etc.)"
echo "  2. Run the development server: uvicorn app.main:app --reload"
echo "  3. Run tests: uv run pytest"
echo "  4. Access API docs at: http://localhost:8000/docs"
