#!/bin/bash

# Safe commit script - only commit if health check passes
set -e

MESSAGE="$1"

if [ -z "$MESSAGE" ]; then
    echo "Usage: ./safe_commit.sh 'commit message'"
    exit 1
fi

echo "🔍 Running health check before commit..."
if ! ./scripts/check_health.sh; then
    echo "❌ Health check failed - not committing"
    exit 1
fi

echo "📝 Committing changes..."
git add .
git commit -m "$MESSAGE"

echo "✅ Safe commit completed!"
