"""
Pytest configuration for the YCA Collector tests.
"""

from collections.abc import Generator
from contextlib import contextmanager
from typing import Any
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.services.database_service import DatabaseService
from app.services.file_service import FileService
from app.services.io_service import IOService
from app.services.io_service_langgraph import LangGraphIOService
from app.workflows.manager import WorkflowManager


@pytest.fixture
def mock_file_content():
    """Return mock file content for testing."""
    return b"Test file content"


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    session = MagicMock()
    return session


@pytest.fixture
def mock_db_service(mock_db_session):
    """Create a mock database service."""
    db_service = MagicMock(spec=DatabaseService)

    # Mock the get_db method to return a context manager that yields the mock session
    @contextmanager
    def mock_get_db() -> Generator[Any, None, None]:
        try:
            yield mock_db_session
        finally:
            pass

    db_service.get_db.return_value = mock_get_db()

    # Mock CRUD operations
    db_service.get.return_value = MagicMock()
    db_service.create.return_value = MagicMock()
    db_service.update.return_value = MagicMock()
    db_service.delete.return_value = MagicMock()
    db_service.list.return_value = []

    return db_service


@pytest.fixture
def mock_file_service():
    """Create a mock file service."""
    file_service = MagicMock(spec=FileService)
    file_service.upload_file = AsyncMock(return_value="https://example.com/test.pdf")
    file_service.download_file = AsyncMock(return_value=b"Test file content")
    file_service.delete_file = AsyncMock(return_value=True)
    file_service.get_file_url = MagicMock(return_value="https://example.com/test.pdf")

    return file_service


@pytest.fixture
def mock_workflow_manager():
    """Create a mock workflow manager."""
    workflow_manager = MagicMock(spec=WorkflowManager)
    workflow_manager.execute_workflow = AsyncMock(return_value="test-workflow-id")
    workflow_manager.get_workflow_status = MagicMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "status": "completed",
            "result": {"success": True},
        }
    )
    workflow_manager.list_workflows = MagicMock(return_value=[])
    workflow_manager.retry_workflow = AsyncMock(return_value="new-test-workflow-id")
    workflow_manager.cancel_workflow = AsyncMock(return_value=True)

    return workflow_manager


@pytest.fixture
def mock_io_service(mock_db_service, mock_file_service):
    """Create a mock IO service."""
    io_service = MagicMock(spec=IOService)
    io_service.db_service = mock_db_service
    io_service.file_service = mock_file_service

    io_service.process_io_upload = AsyncMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "insertion_order_id": "test-io-id",
            "status": "processing_complete",
            "extracted_data": {"campaign_name": "Test Campaign"},
        }
    )

    io_service.get_workflow_status = AsyncMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "status": "completed",
            "extracted_data": {"campaign_name": "Test Campaign"},
        }
    )

    io_service.list_workflows = AsyncMock(
        return_value={
            "workflows": [
                {
                    "workflow_id": "test-workflow-id",
                    "processing_status": "completed",
                    "created_at": "2023-01-01T00:00:00",
                }
            ],
            "total_count": 1,
        }
    )

    io_service.retry_workflow = AsyncMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "status": "retrying",
            "message": "Workflow retry initiated",
        }
    )

    return io_service


@pytest.fixture
def mock_langgraph_io_service(
    mock_db_service, mock_file_service, mock_workflow_manager
):
    """Create a mock LangGraph IO service."""
    io_service = MagicMock(spec=LangGraphIOService)
    io_service.db_service = mock_db_service
    io_service.file_service = mock_file_service
    io_service.workflow_manager = mock_workflow_manager

    io_service.process_io_upload = AsyncMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "status": "processing_started",
            "message": "IO document processing initiated using LangGraph workflow",
            "processing_type": "langgraph_orchestrated",
        }
    )

    io_service.get_workflow_status = AsyncMock(
        return_value={
            "workflow_id": "test-workflow-id",
            "status": "completed",
            "result": {"success": True},
        }
    )

    io_service.list_workflows = AsyncMock(
        return_value={
            "workflows": [
                {
                    "workflow_id": "test-workflow-id",
                    "workflow_type": "io_processing",
                    "status": "completed",
                }
            ],
            "total_count": 1,
            "filter_applied": None,
        }
    )

    io_service.retry_workflow = AsyncMock(
        return_value={
            "original_workflow_id": "test-workflow-id",
            "new_workflow_id": "new-test-workflow-id",
            "status": "retry_initiated",
        }
    )

    io_service.cancel_workflow = AsyncMock(
        return_value={"workflow_id": "test-workflow-id", "status": "cancelled"}
    )

    io_service.get_workflow_metrics = MagicMock(
        return_value={
            "io_processing_workflows": {
                "total_executions": 10,
                "running_executions": 2,
                "status_breakdown": {
                    "pending": 0,
                    "running": 2,
                    "completed": 7,
                    "failed": 1,
                },
            },
            "service_type": "langgraph_enhanced",
            "features": [
                "async_processing",
                "workflow_orchestration",
                "error_recovery",
                "status_monitoring",
                "retry_capability",
            ],
        }
    )

    return io_service


# Patch core utils for consistent behavior in tests
@pytest.fixture(autouse=True)
def patch_core_utils(monkeypatch):
    """Patch core utilities for consistent behavior in tests."""

    # Patch generate_uuid to return predictable values
    mock_uuid_counter = 0

    def mock_generate_uuid():
        nonlocal mock_uuid_counter
        mock_uuid_counter += 1
        return f"00000000-0000-0000-0000-{mock_uuid_counter:012d}"

    monkeypatch.setattr("app.core.utils.generate_uuid", mock_generate_uuid)

    # Patch get_file_extension to return extension without dot
    def mock_get_file_extension(filename):
        if not filename:
            return ""
        parts = filename.split(".")
        if len(parts) > 1:
            return parts[-1].lower()
        return ""

    monkeypatch.setattr("app.core.utils.get_file_extension", mock_get_file_extension)

    # Patch sanitize_filename to handle special characters
    def mock_sanitize_filename(filename):
        if not filename:
            return "unnamed_file"

        # Replace spaces with underscores
        result = filename.replace(" ", "_")

        # Remove special characters
        import re

        result = re.sub(r"[^\w\.-]", "", result)

        # Handle path traversal attempts
        result = result.replace("..", "").replace("/", "_").replace("\\", "_")

        return result or "unnamed_file"

    monkeypatch.setattr("app.core.utils.sanitize_filename", mock_sanitize_filename)
