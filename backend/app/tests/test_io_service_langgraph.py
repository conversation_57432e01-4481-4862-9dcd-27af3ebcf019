"""
Tests for LangGraph IO service.
"""

from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile

from app.services.io_service_langgraph import (
    LangGraphIOService,
    create_langgraph_io_service,
)
from app.workflows.manager import WorkflowStatus, WorkflowType


class TestLangGraphIOService:
    """Test the LangGraph IO service."""

    @pytest.fixture
    def mock_services(self):
        """Create mock services for testing."""
        db_service = MagicMock()
        file_service = MagicMock()
        workflow_manager = MagicMock()

        return {
            "db_service": db_service,
            "file_service": file_service,
            "workflow_manager": workflow_manager,
        }

    @pytest.fixture
    def io_service(self, mock_services):
        """Create an IO service instance with mock dependencies."""
        return LangGraphIOService(
            mock_services["db_service"],
            mock_services["file_service"],
            mock_services["workflow_manager"],
        )

    @pytest.mark.asyncio
    async def test_process_io_upload_success(self, io_service, mock_services):
        """Test successful IO upload processing."""
        # Setup mock file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.size = 1024
        mock_file.read = AsyncMock(return_value=b"test content")
        mock_file.seek = AsyncMock()

        # Setup mock workflow manager
        mock_services["workflow_manager"].execute_workflow = AsyncMock(
            return_value="test-workflow-id"
        )

        # Process upload
        result = await io_service.process_io_upload(
            file=mock_file,
            advertiser_id="test-advertiser",
            campaign_name="Test Campaign",
        )

        # Verify result
        assert result["workflow_id"] == "test-workflow-id"
        assert result["status"] == "processing_started"
        assert "langgraph" in result["processing_type"]

        # Verify workflow execution
        mock_services["workflow_manager"].execute_workflow.assert_called_once()
        call_args = mock_services["workflow_manager"].execute_workflow.call_args[0]
        assert call_args[0] == WorkflowType.IO_PROCESSING
        assert call_args[1]["advertiser_id"] == "test-advertiser"
        assert call_args[1]["campaign_name"] == "Test Campaign"
        assert call_args[1]["file"]["filename"] == "test.pdf"
        assert call_args[1]["file"]["content_type"] == "application/pdf"

        # Verify file was read and seek was called
        mock_file.read.assert_called_once()
        mock_file.seek.assert_called_once_with(0)

    @pytest.mark.asyncio
    async def test_process_io_upload_error(self, io_service, mock_services):
        """Test error handling in IO upload processing."""
        # Setup mock file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test.pdf"

        # Setup mock workflow manager to raise an exception
        mock_services["workflow_manager"].execute_workflow = AsyncMock(
            side_effect=ValueError("Test error")
        )

        # Process upload should raise an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await io_service.process_io_upload(
                file=mock_file, advertiser_id="test-advertiser"
            )

        # Verify exception
        assert excinfo.value.status_code == 500
        assert "Test error" in str(excinfo.value.detail)

    @pytest.mark.asyncio
    async def test_get_workflow_status_success(self, io_service, mock_services):
        """Test getting workflow status."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].get_workflow_status = MagicMock(
            return_value={
                "workflow_id": "test-id",
                "status": "completed",
                "result": {"success": True},
            }
        )

        # Get status
        status = await io_service.get_workflow_status("test-id")

        # Verify status
        assert status["workflow_id"] == "test-id"
        assert status["status"] == "completed"
        assert status["result"]["success"] is True

        # Verify workflow manager was called
        mock_services["workflow_manager"].get_workflow_status.assert_called_once_with(
            "test-id"
        )

    @pytest.mark.asyncio
    async def test_get_workflow_status_not_found(self, io_service, mock_services):
        """Test getting status of non-existent workflow."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].get_workflow_status = MagicMock(
            return_value=None
        )

        # Get status should raise an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await io_service.get_workflow_status("non-existent")

        # Verify exception
        assert excinfo.value.status_code == 404
        assert "not found" in str(excinfo.value.detail)

    @pytest.mark.asyncio
    async def test_list_workflows(self, io_service, mock_services):
        """Test listing workflows."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].list_workflows = MagicMock(
            return_value=[
                {
                    "workflow_id": "test-id-1",
                    "workflow_type": "io_processing",
                    "status": "completed",
                },
                {
                    "workflow_id": "test-id-2",
                    "workflow_type": "communication",
                    "status": "failed",
                },
            ]
        )

        # List workflows
        result = await io_service.list_workflows()

        # Verify result
        assert result["total_count"] == 1  # Only io_processing workflows
        assert len(result["workflows"]) == 1
        assert result["workflows"][0]["workflow_id"] == "test-id-1"
        assert result["workflows"][0]["workflow_type"] == "io_processing"

        # Verify workflow manager was called
        mock_services["workflow_manager"].list_workflows.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_workflows_with_filter(self, io_service, mock_services):
        """Test listing workflows with status filter."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].list_workflows = MagicMock(return_value=[])

        # List workflows with valid filter
        result = await io_service.list_workflows("completed")

        # Verify result
        assert result["filter_applied"] == "completed"
        assert result["total_count"] == 0

        # Verify workflow manager was called with correct status
        mock_services["workflow_manager"].list_workflows.assert_called_once()
        status_arg = mock_services["workflow_manager"].list_workflows.call_args[0][0]
        assert status_arg == WorkflowStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_list_workflows_invalid_filter(self, io_service):
        """Test listing workflows with invalid status filter."""
        # List workflows with invalid filter should raise an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await io_service.list_workflows("invalid_status")

        # Verify exception
        assert excinfo.value.status_code == 400
        assert "Invalid status filter" in str(excinfo.value.detail)

    @pytest.mark.asyncio
    async def test_retry_workflow_success(self, io_service, mock_services):
        """Test retrying a workflow."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].retry_workflow = AsyncMock(
            return_value="new-test-id"
        )

        # Retry workflow
        result = await io_service.retry_workflow("test-id")

        # Verify result
        assert result["original_workflow_id"] == "test-id"
        assert result["new_workflow_id"] == "new-test-id"
        assert result["status"] == "retry_initiated"

        # Verify workflow manager was called
        mock_services["workflow_manager"].retry_workflow.assert_called_once_with(
            "test-id"
        )

    @pytest.mark.asyncio
    async def test_retry_workflow_failure(self, io_service, mock_services):
        """Test retrying a workflow that can't be retried."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].retry_workflow = AsyncMock(return_value=None)

        # Retry workflow should raise an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await io_service.retry_workflow("test-id")

        # Verify exception
        assert excinfo.value.status_code == 400
        assert "Cannot retry workflow" in str(excinfo.value.detail)

    @pytest.mark.asyncio
    async def test_cancel_workflow_success(self, io_service, mock_services):
        """Test cancelling a workflow."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].cancel_workflow = AsyncMock(return_value=True)

        # Cancel workflow
        result = await io_service.cancel_workflow("test-id")

        # Verify result
        assert result["workflow_id"] == "test-id"
        assert result["status"] == "cancelled"

        # Verify workflow manager was called
        mock_services["workflow_manager"].cancel_workflow.assert_called_once_with(
            "test-id"
        )

    @pytest.mark.asyncio
    async def test_cancel_workflow_failure(self, io_service, mock_services):
        """Test cancelling a workflow that can't be cancelled."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].cancel_workflow = AsyncMock(
            return_value=False
        )

        # Cancel workflow should raise an HTTPException
        with pytest.raises(HTTPException) as excinfo:
            await io_service.cancel_workflow("test-id")

        # Verify exception
        assert excinfo.value.status_code == 400
        assert "Cannot cancel workflow" in str(excinfo.value.detail)

    def test_get_workflow_metrics(self, io_service, mock_services):
        """Test getting workflow metrics."""
        # Setup mock workflow manager
        mock_services["workflow_manager"].get_execution_metrics = MagicMock(
            return_value={
                "total_executions": 10,
                "running_executions": 2,
                "status_breakdown": {
                    "pending": 0,
                    "running": 2,
                    "completed": 7,
                    "failed": 1,
                },
            }
        )

        # Get metrics
        metrics = io_service.get_workflow_metrics()

        # Verify metrics
        assert metrics["io_processing_workflows"]["total_executions"] == 10
        assert metrics["io_processing_workflows"]["running_executions"] == 2
        assert metrics["service_type"] == "langgraph_enhanced"
        assert "workflow_orchestration" in metrics["features"]
        assert "error_recovery" in metrics["features"]

        # Verify workflow manager was called
        mock_services["workflow_manager"].get_execution_metrics.assert_called_once()

    def test_factory_function(self, mock_services):
        """Test the factory function."""
        # Create service using factory function
        service = create_langgraph_io_service(
            mock_services["db_service"],
            mock_services["file_service"],
            mock_services["workflow_manager"],
        )

        # Verify service was created correctly
        assert isinstance(service, LangGraphIOService)
        assert service.db_service == mock_services["db_service"]
        assert service.file_service == mock_services["file_service"]
        assert service.workflow_manager == mock_services["workflow_manager"]
