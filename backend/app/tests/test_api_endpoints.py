"""
Tests for API endpoints.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import FastAP<PERSON>, UploadFile
from fastapi.testclient import TestClient

from app.api.api import api_router
from app.api.endpoints.io_processing import router as io_router


@pytest.fixture
def app():
    """Create a FastAPI app for testing."""
    app = FastAPI()
    app.include_router(api_router)
    return app


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI app."""
    return TestClient(app)


class TestIOProcessingEndpoints:
    """Test IO processing endpoints."""

    @pytest.mark.asyncio
    @patch("app.api.endpoints.io_processing.get_io_service")
    async def test_upload_io(self, mock_get_io_service, app, client):
        """Test the IO upload endpoint."""
        # Setup mock IO service
        mock_io_service = MagicMock()
        mock_io_service.process_io_upload = AsyncMock(
            return_value={
                "workflow_id": "test-workflow-id",
                "status": "processing_started",
                "message": "IO document processing initiated",
            }
        )
        mock_get_io_service.return_value = mock_io_service

        # Create test client with mocked dependencies
        app = FastAPI()
        app.include_router(io_router)
        client = TestClient(app)

        # Make request with mock file content
        response = client.post(
            "/upload",
            files={"file": ("test.pdf", b"test content", "application/pdf")},
            data={"advertiser_id": "test-advertiser", "campaign_name": "Test Campaign"},
        )

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "processing_started"

        # Verify IO service was called
        mock_io_service.process_io_upload.assert_called_once()
        call_args = mock_io_service.process_io_upload.call_args[1]
        assert call_args["advertiser_id"] == "test-advertiser"
        assert call_args["campaign_name"] == "Test Campaign"
        assert isinstance(call_args["file"], UploadFile)

    @pytest.mark.asyncio
    @patch("app.api.endpoints.io_processing.get_io_service")
    async def test_get_workflow_status(self, mock_get_io_service, app, client):
        """Test the workflow status endpoint."""
        # Setup mock IO service
        mock_io_service = MagicMock()
        mock_io_service.get_workflow_status = AsyncMock(
            return_value={
                "workflow_id": "test-workflow-id",
                "status": "completed",
                "extracted_data": {"campaign_name": "Test Campaign"},
            }
        )
        mock_get_io_service.return_value = mock_io_service

        # Create test client with mocked dependencies
        app = FastAPI()
        app.include_router(io_router)
        client = TestClient(app)

        # Make request
        response = client.get("/status/test-workflow-id")

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "completed"
        assert response.json()["extracted_data"]["campaign_name"] == "Test Campaign"

        # Verify IO service was called
        mock_io_service.get_workflow_status.assert_called_once_with("test-workflow-id")

    @pytest.mark.asyncio
    @patch("app.api.endpoints.io_processing.get_io_service")
    async def test_list_workflows(self, mock_get_io_service, app, client):
        """Test the list workflows endpoint."""
        # Setup mock IO service
        mock_io_service = MagicMock()
        mock_io_service.list_workflows = AsyncMock(
            return_value={
                "workflows": [
                    {
                        "workflow_id": "test-workflow-id",
                        "processing_status": "completed",
                        "created_at": "2023-01-01T00:00:00",
                    }
                ],
                "total_count": 1,
            }
        )
        mock_get_io_service.return_value = mock_io_service

        # Create test client with mocked dependencies
        app = FastAPI()
        app.include_router(io_router)
        client = TestClient(app)

        # Make request
        response = client.get("/workflows?limit=10&offset=0")

        # Verify response
        assert response.status_code == 200
        assert len(response.json()["workflows"]) == 1
        assert response.json()["workflows"][0]["workflow_id"] == "test-workflow-id"
        assert response.json()["total_count"] == 1

        # Verify IO service was called
        mock_io_service.list_workflows.assert_called_once_with(10, 0)

    @pytest.mark.asyncio
    @patch("app.api.endpoints.io_processing.get_io_service")
    async def test_retry_workflow(self, mock_get_io_service, app, client):
        """Test the retry workflow endpoint."""
        # Setup mock IO service
        mock_io_service = MagicMock()
        mock_io_service.retry_workflow = AsyncMock(
            return_value={
                "workflow_id": "test-workflow-id",
                "status": "retrying",
                "message": "Workflow retry initiated",
            }
        )
        mock_get_io_service.return_value = mock_io_service

        # Create test client with mocked dependencies
        app = FastAPI()
        app.include_router(io_router)
        client = TestClient(app)

        # Make request
        response = client.post("/workflows/test-workflow-id/retry")

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "retrying"

        # Verify IO service was called
        mock_io_service.retry_workflow.assert_called_once_with("test-workflow-id")
