"""
Tests for IO processing workflow.
"""

from datetime import datetime
from unittest.mock import <PERSON><PERSON>ock

import pytest

from app.workflows.io_processing.workflow import (
    IOProcessingWorkflow,
    create_io_processing_workflow,
)


class TestIOProcessingWorkflow:
    """Test the IO processing workflow."""

    @pytest.fixture
    def workflow(self):
        """Create a workflow instance for testing."""
        return IOProcessingWorkflow()

    @pytest.fixture
    def sample_state(self):
        """Create a sample workflow state for testing."""
        return {
            "workflow_id": "test-workflow-id",
            "workflow_type": "io_processing",
            "status": "in_progress",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "input_data": {
                "file": {
                    "filename": "test.pdf",
                    "content_type": "application/pdf",
                    "size": 1024,
                    "content": b"test content",
                },
                "advertiser_id": "test-advertiser",
                "campaign_name": "Test Campaign",
                "processing_options": {
                    "extract_line_items": True,
                    "validate_data": True,
                    "store_in_database": True,
                },
            },
            "output_data": {},
            "current_step": "initialize",
            "steps_completed": ["initialize"],
            "errors": [],
            "messages": [],
        }

    def test_initialization(self, workflow):
        """Test workflow initialization."""
        assert workflow.config.workflow_type == "io_processing"
        assert workflow.config.max_retries == 3
        assert workflow.config.timeout_seconds == 600  # 10 minutes
        assert workflow.config.enable_logging is True

    def test_validate_upload_success(self, workflow, sample_state):
        """Test successful upload validation."""
        # Process state
        result = workflow._validate_upload(sample_state)

        # Verify result
        assert "validate_upload" in result["steps_completed"]
        assert result["current_step"] == "validate_upload"
        assert not result["errors"]
        assert result["output_data"]["validation"]["valid"] is True
        assert result["output_data"]["validation"]["filename"] == "test.pdf"
        assert result["output_data"]["validation"]["file_type"] == "pdf"

    def test_validate_upload_unsupported_type(self, workflow, sample_state):
        """Test upload validation with unsupported file type."""
        # Modify state to have unsupported file type
        sample_state["input_data"]["file"]["filename"] = "test.xyz"

        # Process state
        result = workflow._validate_upload(sample_state)

        # Verify result
        assert len(result["errors"]) == 1
        assert "Unsupported file type" in result["errors"][0]

    def test_validate_upload_file_too_large(self, workflow, sample_state):
        """Test upload validation with file too large."""
        # Modify state to have large file
        sample_state["input_data"]["file"]["size"] = 20 * 1024 * 1024  # 20MB

        # Process state
        result = workflow._validate_upload(sample_state)

        # Verify result
        assert len(result["errors"]) == 1
        assert "File too large" in result["errors"][0]

    def test_extract_text_pdf(self, workflow, sample_state):
        """Test text extraction from PDF."""
        # Setup validation data
        sample_state["output_data"]["validation"] = {
            "filename": "test.pdf",
            "file_type": "pdf",
            "valid": True,
        }

        # Mock the PDF extraction method
        workflow._extract_pdf_text = MagicMock(return_value="Extracted PDF content")

        # Process state
        result = workflow._extract_text(sample_state)

        # Verify result
        assert "extract_text" in result["steps_completed"]
        assert result["current_step"] == "extract_text"
        assert not result["errors"]
        assert (
            result["output_data"]["text_extraction"]["raw_text"]
            == "Extracted PDF content"
        )
        assert (
            result["output_data"]["text_extraction"]["extraction_method"]
            == "pdf_parser"
        )

        # Verify extraction method was called
        workflow._extract_pdf_text.assert_called_once()

    def test_extract_text_unsupported(self, workflow, sample_state):
        """Test text extraction with unsupported file type."""
        # Setup validation data with unsupported type
        sample_state["output_data"]["validation"] = {
            "filename": "test.xyz",
            "file_type": "xyz",
            "valid": True,
        }

        # Process state
        result = workflow._extract_text(sample_state)

        # Verify result
        assert len(result["errors"]) == 1
        assert "Unsupported file type" in result["errors"][0]

    def test_extract_structured_data(self, workflow, sample_state):
        """Test structured data extraction."""
        # Setup text extraction data
        sample_state["output_data"]["text_extraction"] = {
            "raw_text": "Sample text with campaign details",
            "text_length": 30,
            "extraction_method": "pdf_parser",
        }

        # Mock the AI extraction method
        mock_data = {
            "campaign_name": "Test Campaign",
            "advertiser": "Test Agency",
            "budget": 50000,
        }
        workflow._simulate_ai_extraction = MagicMock(return_value=mock_data)

        # Process state
        result = workflow._extract_structured_data(sample_state)

        # Verify result
        assert "extract_structured_data" in result["steps_completed"]
        assert result["current_step"] == "extract_structured_data"
        assert not result["errors"]
        assert result["output_data"]["structured_data"] == mock_data

        # Verify extraction method was called
        workflow._simulate_ai_extraction.assert_called_once_with(
            "Sample text with campaign details"
        )

    def test_extract_structured_data_no_text(self, workflow, sample_state):
        """Test structured data extraction with no text."""
        # Setup empty text extraction data
        sample_state["output_data"]["text_extraction"] = {
            "raw_text": "",
            "text_length": 0,
            "extraction_method": "pdf_parser",
        }

        # Process state
        result = workflow._extract_structured_data(sample_state)

        # Verify result
        assert len(result["errors"]) == 1
        assert "No text content available" in result["errors"][0]

    def test_store_results(self, workflow, sample_state):
        """Test storing results."""
        # Setup structured data
        sample_state["output_data"]["structured_data"] = {
            "campaign_name": "Test Campaign",
            "advertiser": "Test Agency",
            "budget": 50000,
        }
        sample_state["output_data"]["text_extraction"] = {
            "raw_text": "Sample text with campaign details",
            "text_length": 30,
        }

        # Process state
        result = workflow._store_results(sample_state)

        # Verify result
        assert "store_results" in result["steps_completed"]
        assert result["current_step"] == "store_results"
        assert not result["errors"]
        assert "database_record" in result["output_data"]
        assert (
            result["output_data"]["database_record"]["campaign_name"] == "Test Campaign"
        )
        assert result["output_data"]["database_record"]["status"] == "processed"

    def test_check_validation_result_success(self, workflow, sample_state):
        """Test validation result check with success."""
        # Setup validation data
        sample_state["output_data"]["validation"] = {"valid": True}

        # Check result
        result = workflow._check_validation_result(sample_state)

        # Verify result
        assert result == "continue"

    def test_check_validation_result_failure(self, workflow, sample_state):
        """Test validation result check with failure."""
        # Setup validation data
        sample_state["output_data"]["validation"] = {"valid": False}

        # Check result
        result = workflow._check_validation_result(sample_state)

        # Verify result
        assert result == "error"

    def test_check_validation_result_with_errors(self, workflow, sample_state):
        """Test validation result check with errors."""
        # Setup errors
        sample_state["errors"] = ["Test error"]
        sample_state["output_data"]["validation"] = {"valid": True}

        # Check result
        result = workflow._check_validation_result(sample_state)

        # Verify result
        assert result == "error"

    def test_check_extraction_result_success(self, workflow, sample_state):
        """Test extraction result check with success."""
        # Setup extraction data
        sample_state["output_data"]["text_extraction"] = {"raw_text": "Sample text"}

        # Check result
        result = workflow._check_extraction_result(sample_state)

        # Verify result
        assert result == "continue"

    def test_check_extraction_result_failure(self, workflow, sample_state):
        """Test extraction result check with failure."""
        # Setup extraction data
        sample_state["output_data"]["text_extraction"] = {"raw_text": ""}

        # Check result
        result = workflow._check_extraction_result(sample_state)

        # Verify result
        assert result == "error"

    def test_check_extraction_result_with_errors(self, workflow, sample_state):
        """Test extraction result check with errors."""
        # Setup errors
        sample_state["errors"] = ["Test error"]
        sample_state["output_data"]["text_extraction"] = {"raw_text": "Sample text"}

        # Check result
        result = workflow._check_extraction_result(sample_state)

        # Verify result
        assert result == "error"

    def test_factory_function(self):
        """Test the factory function."""
        # Create workflow using factory function
        workflow = create_io_processing_workflow()

        # Verify workflow was created correctly
        assert isinstance(workflow, IOProcessingWorkflow)
        assert workflow.config.workflow_type == "io_processing"
