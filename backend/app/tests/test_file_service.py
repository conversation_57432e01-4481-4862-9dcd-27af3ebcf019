"""
Tests for file service.
"""

import os
import tempfile
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import UploadFile

from app.services.file_service import FileService


class TestFileService:
    """Test the file service."""

    @pytest.fixture
    def file_service(self):
        """Create a file service for testing."""
        # Create a temporary directory for file storage
        with tempfile.TemporaryDirectory() as temp_dir:
            service = FileService(
                upload_dir=temp_dir, base_url="http://example.com/files"
            )
            yield service

    @pytest.mark.asyncio
    async def test_upload_file(self, file_service):
        """Test uploading a file."""
        # Create a mock upload file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.read = AsyncMock(return_value=b"Test file content")

        # Upload the file
        file_path = await file_service.upload_file(file=mock_file, directory="test_dir")

        # Verify that the file was uploaded
        assert file_path.startswith("test_dir/")
        assert file_path.endswith(".pdf")

        # Verify that the file exists
        full_path = os.path.join(file_service.upload_dir, file_path)
        assert os.path.exists(full_path)

        # Verify the file content
        with open(full_path, "rb") as f:
            content = f.read()
            assert content == b"Test file content"

    @pytest.mark.asyncio
    async def test_upload_file_with_custom_filename(self, file_service):
        """Test uploading a file with a custom filename."""
        # Create a mock upload file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.read = AsyncMock(return_value=b"Test file content")

        # Upload the file with a custom filename
        file_path = await file_service.upload_file(
            file=mock_file, directory="test_dir", filename="custom_name.pdf"
        )

        # Verify that the file was uploaded with the custom name
        assert file_path == "test_dir/custom_name.pdf"

        # Verify that the file exists
        full_path = os.path.join(file_service.upload_dir, file_path)
        assert os.path.exists(full_path)

    @pytest.mark.asyncio
    async def test_upload_file_with_sanitization(self, file_service):
        """Test uploading a file with a filename that needs sanitization."""
        # Create a mock upload file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "test file with spaces.pdf"
        mock_file.content_type = "application/pdf"
        mock_file.read = AsyncMock(return_value=b"Test file content")

        # Upload the file
        file_path = await file_service.upload_file(file=mock_file, directory="test_dir")

        # Verify that the filename was sanitized
        assert "test_file_with_spaces" in file_path

        # Verify that the file exists
        full_path = os.path.join(file_service.upload_dir, file_path)
        assert os.path.exists(full_path)

    @pytest.mark.asyncio
    async def test_download_file(self, file_service):
        """Test downloading a file."""
        # Create a test file
        test_content = b"Test file content"
        test_file_path = "test_dir/test.pdf"
        full_path = os.path.join(file_service.upload_dir, test_file_path)

        # Create the directory
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        # Write the file
        with open(full_path, "wb") as f:
            f.write(test_content)

        # Download the file
        content = await file_service.download_file(test_file_path)

        # Verify the content
        assert content == test_content

    @pytest.mark.asyncio
    async def test_download_file_not_found(self, file_service):
        """Test downloading a non-existent file."""
        # Try to download a non-existent file
        with pytest.raises(FileNotFoundError):
            await file_service.download_file("non_existent.pdf")

    @pytest.mark.asyncio
    async def test_delete_file(self, file_service):
        """Test deleting a file."""
        # Create a test file
        test_file_path = "test_dir/test.pdf"
        full_path = os.path.join(file_service.upload_dir, test_file_path)

        # Create the directory
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        # Write the file
        with open(full_path, "wb") as f:
            f.write(b"Test file content")

        # Verify that the file exists
        assert os.path.exists(full_path)

        # Delete the file
        result = await file_service.delete_file(test_file_path)

        # Verify that the file was deleted
        assert result is True
        assert not os.path.exists(full_path)

    @pytest.mark.asyncio
    async def test_delete_file_not_found(self, file_service):
        """Test deleting a non-existent file."""
        # Try to delete a non-existent file
        result = await file_service.delete_file("non_existent.pdf")

        # Verify that the result is False
        assert result is False

    def test_get_file_url(self, file_service):
        """Test getting a file URL."""
        # Get the URL for a file
        url = file_service.get_file_url("test_dir/test.pdf")

        # Verify the URL
        assert url == "http://example.com/files/test_dir/test.pdf"
