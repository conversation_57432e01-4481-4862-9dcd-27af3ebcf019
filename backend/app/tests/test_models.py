"""
Tests for database models and schemas.
"""

from datetime import datetime
from uuid import UUID

from app.models.database_models import (
    Advertiser,
    Campaign,
    CreativeAsset,
    InsertionOrder,
    Publisher,
)
from app.models.schemas import (
    AdvertiserCreate,
    CampaignCreate,
    CreativeAssetCreate,
    InsertionOrderCreate,
    InsertionOrderExtractedData,
    PublisherCreate,
)


class TestDatabaseModels:
    """Test database models."""

    def test_publisher_model(self):
        """Test Publisher model."""
        publisher = Publisher(
            id=UUID("00000000-0000-0000-0000-000000000001"),
            name="Test Publisher",
            domain="test-publisher.com",
            email="<EMAIL>",
            settings={"api_key": "test-key"},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        assert publisher.id == UUID("00000000-0000-0000-0000-000000000001")
        assert publisher.name == "Test Publisher"
        assert publisher.domain == "test-publisher.com"
        assert publisher.email == "<EMAIL>"
        assert publisher.settings["api_key"] == "test-key"
        assert isinstance(publisher.created_at, datetime)
        assert isinstance(publisher.updated_at, datetime)

    def test_advertiser_model(self):
        """Test Advertiser model."""
        advertiser = Advertiser(
            id=UUID("00000000-0000-0000-0000-000000000002"),
            name="Test Advertiser",
            contact_name="John Doe",
            contact_email="<EMAIL>",
            publisher_id=UUID("00000000-0000-0000-0000-000000000001"),
            settings={"industry": "technology"},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        assert advertiser.id == UUID("00000000-0000-0000-0000-000000000002")
        assert advertiser.name == "Test Advertiser"
        assert advertiser.contact_name == "John Doe"
        assert advertiser.contact_email == "<EMAIL>"
        assert advertiser.publisher_id == UUID("00000000-0000-0000-0000-000000000001")
        assert advertiser.settings["industry"] == "technology"
        assert isinstance(advertiser.created_at, datetime)
        assert isinstance(advertiser.updated_at, datetime)

    def test_campaign_model(self):
        """Test Campaign model."""
        campaign = Campaign(
            id=UUID("00000000-0000-0000-0000-000000000003"),
            name="Test Campaign",
            advertiser_id=UUID("00000000-0000-0000-0000-000000000002"),
            start_date=datetime.utcnow().date(),
            end_date=datetime.utcnow().date(),
            status="active",
            settings={"budget": 10000},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        assert campaign.id == UUID("00000000-0000-0000-0000-000000000003")
        assert campaign.name == "Test Campaign"
        assert campaign.advertiser_id == UUID("00000000-0000-0000-0000-000000000002")
        assert campaign.status == "active"
        assert campaign.settings["budget"] == 10000
        assert isinstance(campaign.created_at, datetime)
        assert isinstance(campaign.updated_at, datetime)

    def test_insertion_order_model(self):
        """Test InsertionOrder model."""
        insertion_order = InsertionOrder(
            id=UUID("00000000-0000-0000-0000-000000000004"),
            campaign_id=UUID("00000000-0000-0000-0000-000000000003"),
            file_path="ios/test.pdf",
            file_type="application/pdf",
            processing_status="processed",
            extracted_data={"campaign_name": "Test Campaign", "budget": 10000},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        assert insertion_order.id == UUID("00000000-0000-0000-0000-000000000004")
        assert insertion_order.campaign_id == UUID(
            "00000000-0000-0000-0000-000000000003"
        )
        assert insertion_order.file_path == "ios/test.pdf"
        assert insertion_order.file_type == "application/pdf"
        assert insertion_order.processing_status == "processed"
        assert insertion_order.extracted_data["campaign_name"] == "Test Campaign"
        assert insertion_order.extracted_data["budget"] == 10000
        assert isinstance(insertion_order.created_at, datetime)
        assert isinstance(insertion_order.updated_at, datetime)

    def test_creative_asset_model(self):
        """Test CreativeAsset model."""
        creative_asset = CreativeAsset(
            id=UUID("00000000-0000-0000-0000-000000000005"),
            campaign_id=UUID("00000000-0000-0000-0000-000000000003"),
            file_path="assets/test.jpg",
            file_type="image/jpeg",
            width=300,
            height=250,
            size_bytes=10240,
            status="approved",
            metadata={"format": "jpg", "dimensions": "300x250"},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        assert creative_asset.id == UUID("00000000-0000-0000-0000-000000000005")
        assert creative_asset.campaign_id == UUID(
            "00000000-0000-0000-0000-000000000003"
        )
        assert creative_asset.file_path == "assets/test.jpg"
        assert creative_asset.file_type == "image/jpeg"
        assert creative_asset.width == 300
        assert creative_asset.height == 250
        assert creative_asset.size_bytes == 10240
        assert creative_asset.status == "approved"
        assert creative_asset.metadata["format"] == "jpg"
        assert creative_asset.metadata["dimensions"] == "300x250"
        assert isinstance(creative_asset.created_at, datetime)
        assert isinstance(creative_asset.updated_at, datetime)


class TestSchemas:
    """Test Pydantic schemas."""

    def test_publisher_create_schema(self):
        """Test PublisherCreate schema."""
        data = {
            "name": "Test Publisher",
            "domain": "test-publisher.com",
            "email": "<EMAIL>",
            "settings": {"api_key": "test-key"},
        }
        schema = PublisherCreate(**data)

        assert schema.name == "Test Publisher"
        assert schema.domain == "test-publisher.com"
        assert schema.email == "<EMAIL>"
        assert schema.settings["api_key"] == "test-key"

    def test_advertiser_create_schema(self):
        """Test AdvertiserCreate schema."""
        data = {
            "name": "Test Advertiser",
            "contact_name": "John Doe",
            "contact_email": "<EMAIL>",
            "publisher_id": "00000000-0000-0000-0000-000000000001",
            "settings": {"industry": "technology"},
        }
        schema = AdvertiserCreate(**data)

        assert schema.name == "Test Advertiser"
        assert schema.contact_name == "John Doe"
        assert schema.contact_email == "<EMAIL>"
        assert schema.publisher_id == UUID("00000000-0000-0000-0000-000000000001")
        assert schema.settings["industry"] == "technology"

    def test_campaign_create_schema(self):
        """Test CampaignCreate schema."""
        data = {
            "name": "Test Campaign",
            "advertiser_id": "00000000-0000-0000-0000-000000000002",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "status": "active",
            "settings": {"budget": 10000},
        }
        schema = CampaignCreate(**data)

        assert schema.name == "Test Campaign"
        assert schema.advertiser_id == UUID("00000000-0000-0000-0000-000000000002")
        assert str(schema.start_date) == "2023-01-01"
        assert str(schema.end_date) == "2023-12-31"
        assert schema.status == "active"
        assert schema.settings["budget"] == 10000

    def test_insertion_order_create_schema(self):
        """Test InsertionOrderCreate schema."""
        data = {
            "campaign_id": "00000000-0000-0000-0000-000000000003",
            "file_path": "ios/test.pdf",
            "file_type": "application/pdf",
            "processing_status": "uploaded",
            "extracted_data": {"campaign_name": "Test Campaign", "budget": 10000},
        }
        schema = InsertionOrderCreate(**data)

        assert schema.campaign_id == UUID("00000000-0000-0000-0000-000000000003")
        assert schema.file_path == "ios/test.pdf"
        assert schema.file_type == "application/pdf"
        assert schema.processing_status == "uploaded"
        assert schema.extracted_data["campaign_name"] == "Test Campaign"
        assert schema.extracted_data["budget"] == 10000

    def test_creative_asset_create_schema(self):
        """Test CreativeAssetCreate schema."""
        data = {
            "campaign_id": "00000000-0000-0000-0000-000000000003",
            "file_path": "assets/test.jpg",
            "file_type": "image/jpeg",
            "width": 300,
            "height": 250,
            "size_bytes": 10240,
            "status": "pending",
            "metadata": {"format": "jpg", "dimensions": "300x250"},
        }
        schema = CreativeAssetCreate(**data)

        assert schema.campaign_id == UUID("00000000-0000-0000-0000-000000000003")
        assert schema.file_path == "assets/test.jpg"
        assert schema.file_type == "image/jpeg"
        assert schema.width == 300
        assert schema.height == 250
        assert schema.size_bytes == 10240
        assert schema.status == "pending"
        assert schema.metadata["format"] == "jpg"
        assert schema.metadata["dimensions"] == "300x250"

    def test_insertion_order_extracted_data_schema(self):
        """Test InsertionOrderExtractedData schema."""
        data = {
            "campaign_name": "Test Campaign",
            "advertiser_name": "Test Advertiser",
            "flight_dates": {"start": "2023-01-01", "end": "2023-12-31"},
            "budget": 10000,
            "line_items": [
                {"name": "Banner Ad", "format": "300x250", "quantity": 5},
                {"name": "Video Ad", "format": "1920x1080", "quantity": 2},
            ],
            "contacts": [
                {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "role": "Account Manager",
                }
            ],
        }
        schema = InsertionOrderExtractedData(**data)

        assert schema.campaign_name == "Test Campaign"
        assert schema.advertiser_name == "Test Advertiser"
        assert schema.flight_dates["start"] == "2023-01-01"
        assert schema.flight_dates["end"] == "2023-12-31"
        assert schema.budget == 10000
        assert len(schema.line_items) == 2
        assert schema.line_items[0]["name"] == "Banner Ad"
        assert schema.line_items[0]["format"] == "300x250"
        assert schema.line_items[0]["quantity"] == 5
        assert len(schema.contacts) == 1
        assert schema.contacts[0]["name"] == "John Doe"
        assert schema.contacts[0]["email"] == "<EMAIL>"
