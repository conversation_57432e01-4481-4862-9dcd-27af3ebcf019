"""
Tests for API endpoints with mocked dependencies.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

from app.api.endpoints.io_processing import router as io_router


class TestIOProcessingEndpointsMocked:
    """Test IO processing endpoints with mocked dependencies."""

    @pytest.fixture
    def app(self):
        """Create a FastAPI app for testing."""
        app = FastAPI()
        app.include_router(io_router)
        return app

    @pytest.fixture
    def client(self, app):
        """Create a test client for the FastAPI app."""
        return TestClient(app)

    @pytest.fixture
    def mock_io_service(self):
        """Create a mock IO service."""
        mock_service = MagicMock()
        mock_service.process_io_upload = AsyncMock()
        mock_service.get_workflow_status = AsyncMock()
        mock_service.list_workflows = AsyncMock()
        mock_service.retry_workflow = AsyncMock()
        return mock_service

    def test_upload_io(self, app, client, mock_io_service):
        """Test the IO upload endpoint."""
        # Setup mock response
        mock_io_service.process_io_upload.return_value = {
            "workflow_id": "test-workflow-id",
            "status": "processing_started",
            "message": "IO document processing initiated",
        }

        # Patch the dependency
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request with mock file content
            response = client.post(
                "/upload",
                files={"file": ("test.pdf", b"test content", "application/pdf")},
                data={
                    "advertiser_id": "test-advertiser",
                    "campaign_name": "Test Campaign",
                },
            )

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "processing_started"

            # Verify IO service was called
            mock_io_service.process_io_upload.assert_called_once()

    def test_get_workflow_status(self, app, client, mock_io_service):
        """Test the workflow status endpoint."""
        # Setup mock response
        mock_io_service.get_workflow_status.return_value = {
            "workflow_id": "test-workflow-id",
            "status": "completed",
            "extracted_data": {"campaign_name": "Test Campaign"},
        }

        # Patch the dependency
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.get("/status/test-workflow-id")

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "completed"
            assert response.json()["extracted_data"]["campaign_name"] == "Test Campaign"

            # Verify IO service was called
            mock_io_service.get_workflow_status.assert_called_once_with(
                "test-workflow-id"
            )

    def test_list_workflows(self, app, client, mock_io_service):
        """Test the list workflows endpoint."""
        # Setup mock response
        mock_io_service.list_workflows.return_value = {
            "workflows": [
                {
                    "workflow_id": "test-workflow-id",
                    "processing_status": "completed",
                    "created_at": "2023-01-01T00:00:00",
                }
            ],
            "total_count": 1,
        }

        # Patch the dependency
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.get("/workflows?limit=10&offset=0")

            # Verify response
            assert response.status_code == 200
            assert len(response.json()["workflows"]) == 1
            assert response.json()["workflows"][0]["workflow_id"] == "test-workflow-id"
            assert response.json()["total_count"] == 1

            # Verify IO service was called
            mock_io_service.list_workflows.assert_called_once_with(10, 0)

    def test_retry_workflow(self, app, client, mock_io_service):
        """Test the retry workflow endpoint."""
        # Setup mock response
        mock_io_service.retry_workflow.return_value = {
            "workflow_id": "test-workflow-id",
            "status": "retrying",
            "message": "Workflow retry initiated",
        }

        # Patch the dependency
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.post("/workflows/test-workflow-id/retry")

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "retrying"

            # Verify IO service was called
            mock_io_service.retry_workflow.assert_called_once_with("test-workflow-id")
