"""
Tests for database service.
"""

from unittest.mock import As<PERSON><PERSON>ock, MagicMock, patch

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database_models import Publisher
from app.services.database_service import DatabaseService


@pytest.fixture
def mock_session():
    """Create a mock database session."""
    session = MagicMock(spec=AsyncSession)
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.fixture
def db_service(mock_session):
    """Create a database service with a mock session."""
    with patch("app.services.database_service.AsyncSession", return_value=mock_session):
        service = DatabaseService()
        service._session = mock_session
        return service


class TestDatabaseService:
    """Test the database service."""

    @pytest.mark.asyncio
    async def test_get_db(self, db_service):
        """Test the get_db context manager."""
        # Use the context manager
        async with db_service.get_db() as session:
            # Verify that the session is the mock session
            assert session == db_service._session

        # Verify that close was called
        db_service._session.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_create(self, db_service, mock_session):
        """Test creating a database object."""
        # Create a test object
        publisher = Publisher(name="Test Publisher", email="<EMAIL>")

        # Mock the execute method to return a result with inserted_primary_key
        mock_result = MagicMock()
        mock_result.inserted_primary_key = [1]
        mock_session.execute.return_value = mock_result

        # Create the object
        result = await db_service.create(publisher)

        # Verify that execute and commit were called
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once_with(publisher)

        # Verify that the result is the publisher
        assert result == publisher

    @pytest.mark.asyncio
    async def test_get(self, db_service, mock_session):
        """Test getting a database object."""
        # Create a test object
        publisher = Publisher(id=1, name="Test Publisher", email="<EMAIL>")

        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = publisher
        mock_session.execute.return_value = mock_result

        # Get the object
        result = await db_service.get(Publisher, 1)

        # Verify that execute was called with a select statement
        mock_session.execute.assert_called_once()

        # Verify that the result is the publisher
        assert result == publisher

    @pytest.mark.asyncio
    async def test_get_not_found(self, db_service, mock_session):
        """Test getting a non-existent database object."""
        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Get the object
        result = await db_service.get(Publisher, 999)

        # Verify that execute was called with a select statement
        mock_session.execute.assert_called_once()

        # Verify that the result is None
        assert result is None

    @pytest.mark.asyncio
    async def test_update(self, db_service, mock_session):
        """Test updating a database object."""
        # Create a test object
        publisher = Publisher(id=1, name="Test Publisher", email="<EMAIL>")

        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = publisher
        mock_session.execute.return_value = mock_result

        # Update values
        update_data = {"name": "Updated Publisher"}

        # Update the object
        result = await db_service.update(Publisher, 1, update_data)

        # Verify that execute was called twice (get and update)
        assert mock_session.execute.call_count == 2
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once_with(publisher)

        # Verify that the result is the updated publisher
        assert result == publisher
        assert result.name == "Updated Publisher"

    @pytest.mark.asyncio
    async def test_update_not_found(self, db_service, mock_session):
        """Test updating a non-existent database object."""
        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Update values
        update_data = {"name": "Updated Publisher"}

        # Update the object
        result = await db_service.update(Publisher, 999, update_data)

        # Verify that execute was called once (get only)
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_not_called()

        # Verify that the result is None
        assert result is None

    @pytest.mark.asyncio
    async def test_delete(self, db_service, mock_session):
        """Test deleting a database object."""
        # Create a test object
        publisher = Publisher(id=1, name="Test Publisher", email="<EMAIL>")

        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = publisher
        mock_session.execute.return_value = mock_result

        # Delete the object
        result = await db_service.delete(Publisher, 1)

        # Verify that execute was called twice (get and delete)
        assert mock_session.execute.call_count == 2
        mock_session.commit.assert_called_once()

        # Verify that the result is True
        assert result is True

    @pytest.mark.asyncio
    async def test_delete_not_found(self, db_service, mock_session):
        """Test deleting a non-existent database object."""
        # Mock the execute method to return a result with scalar_one_or_none
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Delete the object
        result = await db_service.delete(Publisher, 999)

        # Verify that execute was called once (get only)
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_not_called()

        # Verify that the result is False
        assert result is False

    @pytest.mark.asyncio
    async def test_list(self, db_service, mock_session):
        """Test listing database objects."""
        # Create test objects
        publishers = [
            Publisher(id=1, name="Publisher 1", email="<EMAIL>"),
            Publisher(id=2, name="Publisher 2", email="<EMAIL>"),
            Publisher(id=3, name="Publisher 3", email="<EMAIL>"),
        ]

        # Mock the execute method to return a result with scalars
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = publishers
        mock_session.execute.return_value = mock_result

        # List the objects
        result = await db_service.list(Publisher)

        # Verify that execute was called with a select statement
        mock_session.execute.assert_called_once()

        # Verify that the result is the list of publishers
        assert result == publishers
        assert len(result) == 3

    @pytest.mark.asyncio
    async def test_list_with_skip_and_limit(self, db_service, mock_session):
        """Test listing database objects with skip and limit."""
        # Create test objects
        publishers = [
            Publisher(id=2, name="Publisher 2", email="<EMAIL>"),
            Publisher(id=3, name="Publisher 3", email="<EMAIL>"),
        ]

        # Mock the execute method to return a result with scalars
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = publishers
        mock_session.execute.return_value = mock_result

        # List the objects with skip and limit
        result = await db_service.list(Publisher, skip=1, limit=2)

        # Verify that execute was called with a select statement
        mock_session.execute.assert_called_once()

        # Verify that the result is the list of publishers
        assert result == publishers
        assert len(result) == 2
