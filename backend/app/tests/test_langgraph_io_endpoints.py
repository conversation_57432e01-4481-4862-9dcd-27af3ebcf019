"""
Tests for LangGraph IO processing endpoints with proper mocking.
"""

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from app.api.endpoints.io_processing import router as io_router


@pytest.fixture
def app():
    """Create a FastAPI app for testing."""
    app = FastAPI()
    app.include_router(io_router)
    return app


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI app."""
    return TestClient(app)


class TestLangGraphIOEndpoints:
    """Test LangGraph IO processing endpoints with proper mocking."""

    def test_upload_io_langgraph(self, app, client, mock_langgraph_io_service):
        """Test the IO upload endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.post("/upload/langgraph")
        async def upload_io_langgraph(request):
            # This is a simplified version just for testing
            result = await mock_langgraph_io_service.process_io_upload(None)
            return result

        # Make request
        response = client.post("/upload/langgraph")

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "processing_started"
        assert "langgraph" in response.json()["processing_type"]

    def test_get_workflow_status_langgraph(
        self, app, client, mock_langgraph_io_service
    ):
        """Test the workflow status endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.get("/langgraph/status/{workflow_id}")
        async def get_workflow_status_langgraph(workflow_id: str):
            result = await mock_langgraph_io_service.get_workflow_status(workflow_id)
            return result

        # Make request
        response = client.get("/langgraph/status/test-workflow-id")

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "completed"
        assert response.json()["result"]["success"] is True

        # Verify service was called
        mock_langgraph_io_service.get_workflow_status.assert_called_once_with(
            "test-workflow-id"
        )

    def test_list_workflows_langgraph(self, app, client, mock_langgraph_io_service):
        """Test the list workflows endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.get("/langgraph/workflows")
        async def list_workflows_langgraph():
            result = await mock_langgraph_io_service.list_workflows()
            return result

        # Make request
        response = client.get("/langgraph/workflows")

        # Verify response
        assert response.status_code == 200
        assert len(response.json()["workflows"]) == 1
        assert response.json()["workflows"][0]["workflow_id"] == "test-workflow-id"
        assert response.json()["workflows"][0]["workflow_type"] == "io_processing"
        assert response.json()["total_count"] == 1

    def test_retry_workflow_langgraph(self, app, client, mock_langgraph_io_service):
        """Test the retry workflow endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.post("/langgraph/workflows/{workflow_id}/retry")
        async def retry_workflow_langgraph(workflow_id: str):
            result = await mock_langgraph_io_service.retry_workflow(workflow_id)
            return result

        # Make request
        response = client.post("/langgraph/workflows/test-workflow-id/retry")

        # Verify response
        assert response.status_code == 200
        assert response.json()["original_workflow_id"] == "test-workflow-id"
        assert response.json()["new_workflow_id"] == "new-test-workflow-id"
        assert response.json()["status"] == "retry_initiated"

        # Verify service was called
        mock_langgraph_io_service.retry_workflow.assert_called_once_with(
            "test-workflow-id"
        )

    def test_cancel_workflow_langgraph(self, app, client, mock_langgraph_io_service):
        """Test the cancel workflow endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.post("/langgraph/workflows/{workflow_id}/cancel")
        async def cancel_workflow_langgraph(workflow_id: str):
            result = await mock_langgraph_io_service.cancel_workflow(workflow_id)
            return result

        # Make request
        response = client.post("/langgraph/workflows/test-workflow-id/cancel")

        # Verify response
        assert response.status_code == 200
        assert response.json()["workflow_id"] == "test-workflow-id"
        assert response.json()["status"] == "cancelled"

        # Verify service was called
        mock_langgraph_io_service.cancel_workflow.assert_called_once_with(
            "test-workflow-id"
        )

    def test_get_workflow_metrics_langgraph(
        self, app, client, mock_langgraph_io_service
    ):
        """Test the workflow metrics endpoint with LangGraph."""

        # Create a custom endpoint for LangGraph IO service
        @app.get("/langgraph/metrics")
        def get_workflow_metrics_langgraph():
            result = mock_langgraph_io_service.get_workflow_metrics()
            return result

        # Make request
        response = client.get("/langgraph/metrics")

        # Verify response
        assert response.status_code == 200
        assert response.json()["service_type"] == "langgraph_enhanced"
        assert "workflow_orchestration" in response.json()["features"]
        assert response.json()["io_processing_workflows"]["total_executions"] == 10
