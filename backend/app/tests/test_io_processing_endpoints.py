"""
Tests for IO processing endpoints with proper mocking.
"""

from unittest.mock import patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from app.api.endpoints.io_processing import router as io_router


@pytest.fixture
def app():
    """Create a FastAPI app for testing."""
    app = FastAPI()
    app.include_router(io_router)
    return app


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI app."""
    return TestClient(app)


class TestIOProcessingEndpoints:
    """Test IO processing endpoints with proper mocking."""

    def test_upload_io(self, app, client, mock_io_service):
        """Test the IO upload endpoint."""
        # Patch the dependency to return our mock
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request with mock file content
            response = client.post(
                "/upload",
                files={"file": ("test.pdf", b"test content", "application/pdf")},
                data={
                    "advertiser_id": "test-advertiser",
                    "campaign_name": "Test Campaign",
                },
            )

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "processing_complete"

            # Verify IO service was called
            mock_io_service.process_io_upload.assert_called_once()

    def test_get_workflow_status(self, app, client, mock_io_service):
        """Test the workflow status endpoint."""
        # Patch the dependency to return our mock
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.get("/status/test-workflow-id")

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "completed"
            assert response.json()["extracted_data"]["campaign_name"] == "Test Campaign"

            # Verify IO service was called
            mock_io_service.get_workflow_status.assert_called_once_with(
                "test-workflow-id"
            )

    def test_list_workflows(self, app, client, mock_io_service):
        """Test the list workflows endpoint."""
        # Patch the dependency to return our mock
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.get("/workflows?limit=10&offset=0")

            # Verify response
            assert response.status_code == 200
            assert len(response.json()["workflows"]) == 1
            assert response.json()["workflows"][0]["workflow_id"] == "test-workflow-id"
            assert response.json()["total_count"] == 1

            # Verify IO service was called
            mock_io_service.list_workflows.assert_called_once_with(10, 0)

    def test_retry_workflow(self, app, client, mock_io_service):
        """Test the retry workflow endpoint."""
        # Patch the dependency to return our mock
        with patch(
            "app.api.endpoints.io_processing.get_io_service",
            return_value=mock_io_service,
        ):
            # Make request
            response = client.post("/workflows/test-workflow-id/retry")

            # Verify response
            assert response.status_code == 200
            assert response.json()["workflow_id"] == "test-workflow-id"
            assert response.json()["status"] == "retrying"

            # Verify IO service was called
            mock_io_service.retry_workflow.assert_called_once_with("test-workflow-id")
