"""
Comprehensive tests for core utilities.
"""

import os
import tempfile
import uuid

import pytest

from app.core.utils import (
    create_directory_if_not_exists,
    deep_merge_dicts,
    format_file_size,
    generate_uuid,
    get_file_extension,
    get_file_size,
    parse_date,
    sanitize_filename,
)


class TestGenerateUUID:
    """Test UUID generation."""

    def test_generate_uuid(self):
        """Test that generate_uuid returns a valid UUID."""
        result = generate_uuid()
        assert isinstance(result, str)
        assert len(result) == 36  # UUID string length

        # Verify it's a valid UUID
        try:
            uuid_obj = uuid.UUID(result)
            assert str(uuid_obj) == result
        except ValueError:
            pytest.fail("Generated UUID is not valid")

    def test_generate_uuid_uniqueness(self):
        """Test that generate_uuid returns unique values."""
        uuids = [generate_uuid() for _ in range(100)]
        assert len(uuids) == len(set(uuids))  # All UUIDs should be unique


class TestFileUtils:
    """Test file utility functions."""

    def test_get_file_extension(self):
        """Test file extension extraction."""
        # Test with common file types
        assert get_file_extension("document.pdf") == "pdf"
        assert get_file_extension("spreadsheet.xlsx") == "xlsx"
        assert get_file_extension("presentation.pptx") == "pptx"

        # Test with uppercase extensions
        assert get_file_extension("DOCUMENT.PDF") == "pdf"
        assert get_file_extension("IMAGE.JPG") == "jpg"

        # Test with no extension
        assert get_file_extension("filename") == ""

        # Test with multiple dots
        assert get_file_extension("archive.tar.gz") == "gz"

        # Test with path
        assert get_file_extension("/path/to/document.pdf") == "pdf"

        # Test with empty string
        assert get_file_extension("") == ""

        # Test with None
        with pytest.raises(ValueError):
            get_file_extension(None)

    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Test with valid filename
        assert sanitize_filename("document.pdf") == "document.pdf"

        # Test with spaces
        assert sanitize_filename("my document.pdf") == "my_document.pdf"

        # Test with special characters
        assert sanitize_filename("file@#$%.pdf") == "file.pdf"

        # Test with path traversal attempts
        assert sanitize_filename("../../../etc/passwd") == "etcpasswd"

        # Test with empty string
        assert sanitize_filename("") == "unnamed_file"

        # Test with None
        assert sanitize_filename(None) == "unnamed_file"

    def test_create_directory_if_not_exists(self):
        """Test directory creation."""
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test_dir")

            # Directory should not exist initially
            assert not os.path.exists(test_dir)

            # Create the directory
            create_directory_if_not_exists(test_dir)

            # Directory should now exist
            assert os.path.exists(test_dir)
            assert os.path.isdir(test_dir)

            # Calling the function again should not raise an error
            create_directory_if_not_exists(test_dir)

    def test_get_file_size(self):
        """Test file size calculation."""
        # Create a temporary file with known content
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            content = b"Hello, world!"
            temp_file.write(content)
            temp_file.flush()

            # Get the file size
            size = get_file_size(temp_file.name)

            # Verify the size
            assert size == len(content)

        # Clean up
        os.unlink(temp_file.name)

        # Test with non-existent file
        with pytest.raises(FileNotFoundError):
            get_file_size("non_existent_file.txt")

    def test_format_file_size(self):
        """Test file size formatting."""
        # Test with bytes
        assert format_file_size(500) == "500.00 B"

        # Test with kilobytes
        assert format_file_size(1500) == "1.46 KB"

        # Test with megabytes
        assert format_file_size(1500000) == "1.43 MB"

        # Test with gigabytes
        assert format_file_size(1500000000) == "1.40 GB"

        # Test with custom precision
        assert format_file_size(1500, precision=0) == "1 KB"
        assert format_file_size(1500, precision=1) == "1.5 KB"


class TestDateUtils:
    """Test date utility functions."""

    def test_parse_date(self):
        """Test date parsing."""
        # Parse a date string with default formats
        date = parse_date("2023-01-01")
        assert date is not None
        assert date.year == 2023
        assert date.month == 1
        assert date.day == 1

        # Parse with different formats
        date = parse_date("01/01/2023")
        assert date is not None
        assert date.year == 2023
        assert date.month == 1
        assert date.day == 1

        # Parse with custom format
        date = parse_date("01.01.2023", formats=["%d.%m.%Y"])
        assert date is not None
        assert date.year == 2023
        assert date.month == 1
        assert date.day == 1

        # Parse with invalid format
        date = parse_date("invalid_date")
        assert date is None


class TestDictUtils:
    """Test dictionary utility functions."""

    def test_deep_merge_dicts(self):
        """Test deep merging of dictionaries."""
        # Test with simple dictionaries
        dict1 = {"a": 1, "b": 2}
        dict2 = {"b": 3, "c": 4}
        result = deep_merge_dicts(dict1, dict2)
        assert result == {"a": 1, "b": 3, "c": 4}

        # Test with nested dictionaries
        dict1 = {"a": 1, "b": {"x": 1, "y": 2}}
        dict2 = {"b": {"y": 3, "z": 4}, "c": 5}
        result = deep_merge_dicts(dict1, dict2)
        assert result == {"a": 1, "b": {"x": 1, "y": 3, "z": 4}, "c": 5}

        # Test with empty dictionaries
        assert deep_merge_dicts({}, {}) == {}
        assert deep_merge_dicts(dict1, {}) == dict1
        assert deep_merge_dicts({}, dict2) == dict2
