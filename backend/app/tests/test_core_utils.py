"""
Tests for core utilities.
"""

import uuid

import pytest

from app.core.utils import generate_uuid, get_file_extension, sanitize_filename


def test_generate_uuid():
    """Test UUID generation."""
    # Test that it generates a valid UUID
    result = generate_uuid()
    assert isinstance(result, str)

    # Test that it's a valid UUID format
    try:
        uuid_obj = uuid.UUID(result)
        assert str(uuid_obj) == result
    except ValueError:
        pytest.fail("Generated UUID is not valid")


def test_get_file_extension():
    """Test file extension extraction."""
    # Test with common file types
    assert get_file_extension("document.pdf") == "pdf"
    assert get_file_extension("spreadsheet.xlsx") == "xlsx"
    assert get_file_extension("presentation.pptx") == "pptx"

    # Test with uppercase extensions
    assert get_file_extension("DOCUMENT.PDF") == "pdf"
    assert get_file_extension("IMAGE.JPG") == "jpg"

    # Test with no extension
    assert get_file_extension("filename") == ""

    # Test with multiple dots
    assert get_file_extension("archive.tar.gz") == "gz"

    # Test with path
    assert get_file_extension("/path/to/document.pdf") == "pdf"

    # Test with empty string
    assert get_file_extension("") == ""

    # Test with None
    with pytest.raises(ValueError):
        get_file_extension(None)


def test_sanitize_filename():
    """Test filename sanitization."""
    # Test with valid filename
    assert sanitize_filename("document.pdf") == "document.pdf"

    # Test with spaces
    assert sanitize_filename("my document.pdf") == "my_document.pdf"

    # Test with special characters
    assert sanitize_filename("file@#$%.pdf") == "file.pdf"

    # Test with path traversal attempts
    assert sanitize_filename("../../../etc/passwd") == "etc_passwd"

    # Test with empty string
    assert sanitize_filename("") == "unnamed_file"

    # Test with None
    assert sanitize_filename(None) == "unnamed_file"
