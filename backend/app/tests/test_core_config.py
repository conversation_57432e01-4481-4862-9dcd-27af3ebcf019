"""
Tests for core configuration.
"""

import os
from unittest.mock import patch

from app.core.config import Settings, get_settings


class TestSettings:
    """Test Settings class."""

    def test_default_settings(self):
        """Test default settings."""
        settings = Settings()

        # Check default values
        assert settings.PROJECT_NAME == "YCA Collector"
        assert settings.API_V1_STR == "/api/v1"
        assert settings.BACKEND_CORS_ORIGINS == ["*"]
        assert settings.ENVIRONMENT == "dev"

    @patch.dict(os.environ, {"PROJECT_NAME": "Test Project"})
    def test_override_from_env(self):
        """Test overriding settings from environment variables."""
        settings = Settings()

        # Check overridden value
        assert settings.PROJECT_NAME == "Test Project"

    @patch.dict(
        os.environ, {"BACKEND_CORS_ORIGINS": "http://localhost,https://example.com"}
    )
    def test_parse_cors_origins(self):
        """Test parsing CORS origins from environment variable."""
        settings = Settings()

        # Check parsed value
        assert settings.BACKEND_CORS_ORIGINS == [
            "http://localhost",
            "https://example.com",
        ]

    @patch.dict(os.environ, {"ENVIRONMENT": "production"})
    def test_production_environment(self):
        """Test production environment settings."""
        settings = Settings()

        # Check production-specific settings
        assert settings.ENVIRONMENT == "production"
        assert not settings.DEBUG


class TestGetSettings:
    """Test get_settings function."""

    def test_singleton_pattern(self):
        """Test that get_settings returns the same instance."""
        settings1 = get_settings()
        settings2 = get_settings()

        # Check that both instances are the same object
        assert settings1 is settings2
