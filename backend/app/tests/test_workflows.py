"""
Tests for workflow system.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.workflows.base import (
    BaseWorkflow,
    BaseWorkflowConfig,
    create_workflow_state,
)
from app.workflows.manager import (
    WorkflowExecution,
    WorkflowManager,
    WorkflowStatus,
    WorkflowType,
)


class TestBaseWorkflow:
    """Test the base workflow functionality."""

    def test_initialization(self):
        """Test workflow initialization."""
        config = BaseWorkflowConfig(workflow_type="test_workflow")
        workflow = BaseWorkflow(config)

        assert workflow.config.workflow_type == "test_workflow"
        assert workflow.config.max_retries == 3  # Default value
        assert workflow.config.timeout_seconds == 300  # Default value
        assert workflow.config.enable_logging is True  # Default value
        assert workflow.config.enable_monitoring is True  # Default value

    def test_create_workflow_state(self):
        """Test workflow state creation."""
        input_data = {"key": "value"}
        state = create_workflow_state("test_workflow", input_data)

        assert state["workflow_type"] == "test_workflow"
        assert state["status"] == "pending"
        assert state["input_data"] == input_data
        assert state["output_data"] == {}
        assert state["current_step"] == "created"
        assert state["steps_completed"] == []
        assert state["errors"] == []
        assert state["messages"] == []
        assert isinstance(state["workflow_id"], str)
        assert isinstance(state["created_at"], datetime)
        assert isinstance(state["updated_at"], datetime)


@pytest.mark.asyncio
class TestWorkflowManager:
    """Test the workflow manager."""

    async def test_execute_workflow(self):
        """Test executing a workflow."""
        manager = WorkflowManager()

        # Mock the _run_workflow method
        manager._run_workflow = AsyncMock()

        # Execute a workflow
        input_data = {"test": "data"}
        workflow_id = await manager.execute_workflow(
            WorkflowType.IO_PROCESSING, input_data
        )

        # Verify execution record was created
        assert workflow_id in manager._executions
        execution = manager._executions[workflow_id]
        assert execution.workflow_type == WorkflowType.IO_PROCESSING
        assert execution.input_data == input_data
        assert execution.status == WorkflowStatus.PENDING

        # Verify _run_workflow was called
        manager._run_workflow.assert_called_once()

    async def test_get_workflow_status(self):
        """Test getting workflow status."""
        manager = WorkflowManager()

        # Create a test execution
        execution = WorkflowExecution(
            "test-id", WorkflowType.IO_PROCESSING, {"test": "data"}
        )
        execution.status = WorkflowStatus.COMPLETED
        execution.started_at = datetime.utcnow()
        execution.completed_at = datetime.utcnow()
        execution.result = {"success": True}
        manager._executions["test-id"] = execution

        # Get status
        status = manager.get_workflow_status("test-id")

        # Verify status
        assert status["workflow_id"] == "test-id"
        assert status["workflow_type"] == "io_processing"
        assert status["status"] == "completed"
        assert status["result"] == {"success": True}

        # Test non-existent workflow
        assert manager.get_workflow_status("non-existent") is None

    async def test_list_workflows(self):
        """Test listing workflows."""
        manager = WorkflowManager()

        # Create test executions
        execution1 = WorkflowExecution(
            "test-id-1", WorkflowType.IO_PROCESSING, {"test": "data1"}
        )
        execution1.status = WorkflowStatus.COMPLETED
        manager._executions["test-id-1"] = execution1

        execution2 = WorkflowExecution(
            "test-id-2", WorkflowType.IO_PROCESSING, {"test": "data2"}
        )
        execution2.status = WorkflowStatus.FAILED
        execution2.error = "Test error"
        manager._executions["test-id-2"] = execution2

        # List all workflows
        workflows = manager.list_workflows()
        assert len(workflows) == 2

        # List by status
        completed = manager.list_workflows(WorkflowStatus.COMPLETED)
        assert len(completed) == 1
        assert completed[0]["workflow_id"] == "test-id-1"
        assert completed[0]["status"] == "completed"

        failed = manager.list_workflows(WorkflowStatus.FAILED)
        assert len(failed) == 1
        assert failed[0]["workflow_id"] == "test-id-2"
        assert failed[0]["status"] == "failed"
        assert failed[0]["has_error"] is True

    async def test_cancel_workflow(self):
        """Test cancelling a workflow."""
        manager = WorkflowManager()

        # Create a test execution with a mock task
        execution = WorkflowExecution(
            "test-id", WorkflowType.IO_PROCESSING, {"test": "data"}
        )
        execution.status = WorkflowStatus.RUNNING
        manager._executions["test-id"] = execution

        # Create a mock task
        mock_task = MagicMock()
        mock_task.done.return_value = False
        manager._running_tasks["test-id"] = mock_task

        # Cancel the workflow
        result = await manager.cancel_workflow("test-id")

        # Verify result
        assert result is True
        assert execution.status == WorkflowStatus.CANCELLED
        mock_task.cancel.assert_called_once()

        # Test cancelling non-existent workflow
        result = await manager.cancel_workflow("non-existent")
        assert result is False

    async def test_retry_workflow(self):
        """Test retrying a workflow."""
        manager = WorkflowManager()

        # Mock execute_workflow
        original_execute = manager.execute_workflow
        manager.execute_workflow = AsyncMock(return_value="new-test-id")

        # Create a failed execution
        execution = WorkflowExecution(
            "test-id", WorkflowType.IO_PROCESSING, {"test": "data"}
        )
        execution.status = WorkflowStatus.FAILED
        execution.error = "Test error"
        manager._executions["test-id"] = execution

        # Retry the workflow
        new_id = await manager.retry_workflow("test-id")

        # Verify result
        assert new_id == "new-test-id"
        manager.execute_workflow.assert_called_once_with(
            WorkflowType.IO_PROCESSING, {"test": "data"}
        )

        # Test retrying non-failed workflow
        execution.status = WorkflowStatus.COMPLETED
        new_id = await manager.retry_workflow("test-id")
        assert new_id is None

        # Restore original method
        manager.execute_workflow = original_execute
