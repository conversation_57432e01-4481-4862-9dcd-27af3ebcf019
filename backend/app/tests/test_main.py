"""
Tests for the main application.
"""

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import Test<PERSON><PERSON>

from app.main import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


class TestMainApplication:
    """Test the main application."""

    def test_app_instance(self):
        """Test that the app is a FastAPI instance."""
        assert isinstance(app, FastAPI)
        assert app.title == "YCA Collector"

    def test_root_endpoint(self, client):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        assert response.json() == {"message": "Welcome to YCA Collector API"}

    def test_health_check(self, client):
        """Test the health check endpoint."""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        assert "status" in response.json()
        assert response.json()["status"] == "ok"

    def test_api_v1_prefix(self):
        """Test that the API v1 prefix is set correctly."""
        # Find the router with the API v1 prefix
        api_v1_router = None
        for route in app.routes:
            if hasattr(route, "prefix") and route.prefix == "/api/v1":
                api_v1_router = route
                break

        # Verify that the router exists
        assert api_v1_router is not None

    def test_cors_middleware(self):
        """Test that CORS middleware is configured."""
        # Check that the CORS middleware is in the app's middleware stack
        cors_middleware = None
        for middleware in app.user_middleware:
            if middleware.cls.__name__ == "CORSMiddleware":
                cors_middleware = middleware
                break

        # Verify that the middleware exists
        assert cors_middleware is not None

    def test_exception_handlers(self):
        """Test that exception handlers are configured."""
        # Check that the HTTPException handler is registered
        assert 422 in app.exception_handlers
        assert 500 in app.exception_handlers
