"""
Tests for document parsers.
"""

from unittest.mock import MagicMock, patch

from app.services.parsers.base import ParsedDocument
from app.services.parsers.csv import CSVParser
from app.services.parsers.docx import DocxParser
from app.services.parsers.factory import DocumentParserFactory
from app.services.parsers.pdf import PDFParser
from app.services.parsers.xlsx import XlsxParser


class TestDocumentParserFactory:
    """Test the document parser factory."""

    def test_get_supported_types(self):
        """Test getting supported MIME types."""
        factory = DocumentParserFactory()
        supported_types = factory.get_supported_types()

        # Check that common types are supported
        assert "application/pdf" in supported_types
        assert "text/csv" in supported_types
        assert (
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            in supported_types
        )
        assert (
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            in supported_types
        )

    def test_get_parser_by_mime_type(self):
        """Test getting parsers by MIME type."""
        factory = DocumentParserFactory()

        # Test PDF parser
        pdf_parser = factory.get_parser("file.txt", "application/pdf")
        assert isinstance(pdf_parser, PDFParser)

        # Test DOCX parser
        docx_parser = factory.get_parser(
            "file.txt",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )
        assert isinstance(docx_parser, DocxParser)

        # Test XLSX parser
        xlsx_parser = factory.get_parser(
            "file.txt",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        assert isinstance(xlsx_parser, XlsxParser)

        # Test CSV parser
        csv_parser = factory.get_parser("file.txt", "text/csv")
        assert isinstance(csv_parser, CSVParser)

    def test_get_parser_by_extension(self):
        """Test getting parsers by file extension."""
        factory = DocumentParserFactory()

        # Test PDF parser
        pdf_parser = factory.get_parser("document.pdf")
        assert isinstance(pdf_parser, PDFParser)

        # Test DOCX parser
        docx_parser = factory.get_parser("document.docx")
        assert isinstance(docx_parser, DocxParser)

        # Test XLSX parser
        xlsx_parser = factory.get_parser("spreadsheet.xlsx")
        assert isinstance(xlsx_parser, XlsxParser)

        # Test CSV parser
        csv_parser = factory.get_parser("data.csv")
        assert isinstance(csv_parser, CSVParser)

    def test_unsupported_type(self):
        """Test handling of unsupported file types."""
        factory = DocumentParserFactory()

        # Test unsupported MIME type
        unsupported_parser = factory.get_parser("file.txt", "application/unknown")
        assert unsupported_parser is None

        # Test unsupported extension
        unsupported_parser = factory.get_parser("file.xyz")
        assert unsupported_parser is None


class TestPDFParser:
    """Test the PDF parser."""

    @patch("app.services.parsers.pdf.PdfReader")
    def test_parse(self, mock_pdf_reader):
        """Test parsing PDF content."""
        # Setup mock
        mock_reader_instance = MagicMock()
        mock_pdf_reader.return_value = mock_reader_instance

        # Mock pages
        mock_page1 = MagicMock()
        mock_page1.extract_text.return_value = "Page 1 content"
        mock_page2 = MagicMock()
        mock_page2.extract_text.return_value = "Page 2 content"
        mock_reader_instance.pages = [mock_page1, mock_page2]

        # Mock metadata
        mock_reader_instance.metadata = {
            "Author": "Test Author",
            "Title": "Test Document",
        }

        # Create parser and parse content
        parser = PDFParser()
        result = parser.parse(b"dummy pdf content")

        # Verify result
        assert isinstance(result, ParsedDocument)
        assert result.content == "Page 1 contentPage 2 content"
        assert result.metadata["Author"] == "Test Author"
        assert result.metadata["Title"] == "Test Document"
        assert result.metadata["page_count"] == 2
        assert result.file_type == "application/pdf"


class TestDocxParser:
    """Test the DOCX parser."""

    @patch("app.services.parsers.docx.docx2txt")
    def test_parse(self, mock_docx2txt):
        """Test parsing DOCX content."""
        # Setup mock
        mock_docx2txt.process.return_value = "DOCX document content"

        # Create parser and parse content
        parser = DocxParser()
        result = parser.parse(b"dummy docx content")

        # Verify result
        assert isinstance(result, ParsedDocument)
        assert result.content == "DOCX document content"
        assert result.metadata["format"] == "DOCX"
        assert result.metadata["size_bytes"] == len(b"dummy docx content")
        assert (
            result.file_type
            == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )


class TestXlsxParser:
    """Test the XLSX parser."""

    @patch("app.services.parsers.xlsx.openpyxl")
    def test_parse(self, mock_openpyxl):
        """Test parsing XLSX content."""
        # Setup mock
        mock_workbook = MagicMock()
        mock_openpyxl.load_workbook.return_value = mock_workbook

        # Mock sheets
        mock_sheet1 = MagicMock()
        mock_sheet1.iter_rows.return_value = [
            [MagicMock(value="A1"), MagicMock(value="B1")],
            [MagicMock(value="A2"), MagicMock(value="B2")],
        ]

        mock_sheet2 = MagicMock()
        mock_sheet2.iter_rows.return_value = [
            [MagicMock(value="C1"), MagicMock(value="D1")],
            [MagicMock(value="C2"), MagicMock(value="D2")],
        ]

        mock_workbook.sheetnames = ["Sheet1", "Sheet2"]
        mock_workbook.__getitem__.side_effect = (
            lambda name: mock_sheet1 if name == "Sheet1" else mock_sheet2
        )

        # Create parser and parse content
        parser = XlsxParser()
        result = parser.parse(b"dummy xlsx content")

        # Verify result
        assert isinstance(result, ParsedDocument)
        assert "Sheet: Sheet1" in result.content
        assert "A1\tB1" in result.content
        assert "A2\tB2" in result.content
        assert "Sheet: Sheet2" in result.content
        assert "C1\tD1" in result.content
        assert "C2\tD2" in result.content
        assert result.metadata["format"] == "XLSX"
        assert result.metadata["sheet_count"] == 2
        assert result.metadata["sheet_names"] == ["Sheet1", "Sheet2"]
        assert result.metadata["size_bytes"] == len(b"dummy xlsx content")
        assert (
            result.file_type
            == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )


class TestCSVParser:
    """Test the CSV parser."""

    @patch("app.services.parsers.csv.csv.reader")
    def test_parse(self, mock_csv_reader):
        """Test parsing CSV content."""
        # Setup mock
        mock_csv_reader.return_value = [
            ["Header1", "Header2", "Header3"],
            ["Value1", "Value2", "Value3"],
            ["Value4", "Value5", "Value6"],
        ]

        # Create parser and parse content
        parser = CSVParser()
        result = parser.parse(b"dummy,csv,content\n1,2,3")

        # Verify result
        assert isinstance(result, ParsedDocument)
        assert "Header1\tHeader2\tHeader3" in result.content
        assert "Value1\tValue2\tValue3" in result.content
        assert "Value4\tValue5\tValue6" in result.content
        assert result.metadata["format"] == "CSV"
        assert result.metadata["row_count"] == 3
        assert result.metadata["column_count"] == 3
        assert result.metadata["headers"] == ["Header1", "Header2", "Header3"]
        assert result.metadata["size_bytes"] == len(b"dummy,csv,content\n1,2,3")
        assert result.file_type == "text/csv"
