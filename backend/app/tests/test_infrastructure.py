"""
Infrastructure tests to verify basic project setup.

These tests ensure that the core infrastructure is working correctly
and can be extended as more functionality is added.
"""

import pytest


def test_python_version():
    """Test that we're using Python 3.11+."""
    import sys

    assert sys.version_info >= (3, 11), "Python 3.11+ is required"


def test_imports():
    """Test that core dependencies can be imported."""
    try:
        import fastapi  # noqa: F401
        import pydantic  # noqa: F401
        import sqlalchemy  # noqa: F401

        assert True
    except ImportError as e:
        pytest.fail(f"Core dependency import failed: {e}")


def test_app_structure():
    """Test that the app package structure is correct."""
    import app
    import app.api
    import app.core
    import app.models
    import app.services

    assert hasattr(app, "__package__")


@pytest.mark.asyncio
async def test_async_support():
    """Test that async/await works correctly."""

    async def async_function():
        return "async works"

    result = await async_function()
    assert result == "async works"
