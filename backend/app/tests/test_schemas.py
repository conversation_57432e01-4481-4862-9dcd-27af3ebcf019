"""
Tests for Pydantic schemas.
"""

from uuid import UUID

from app.models.schemas import (
    AdvertiserCreate,
    CampaignCreate,
    InsertionOrderCreate,
    InsertionOrderExtractedData,
    InsertionOrderUpdate,
    PublisherCreate,
)


class TestSchemas:
    """Test Pydantic schemas."""

    def test_publisher_create_schema(self):
        """Test PublisherCreate schema."""
        data = {"name": "Test Publisher", "email": "<EMAIL>"}
        schema = PublisherCreate(**data)

        assert schema.name == "Test Publisher"
        assert schema.email == "<EMAIL>"

    def test_advertiser_create_schema(self):
        """Test AdvertiserCreate schema."""
        data = {
            "name": "Test Advertiser",
            "email": "<EMAIL>",
            "contact_person": "<PERSON>",
            "publisher_id": "00000000-0000-0000-0000-000000000001",
        }
        schema = AdvertiserCreate(**data)

        assert schema.name == "Test Advertiser"
        assert schema.email == "<EMAIL>"
        assert schema.contact_person == "<PERSON>"
        assert schema.publisher_id == UUID("00000000-0000-0000-0000-000000000001")

    def test_campaign_create_schema(self):
        """Test CampaignCreate schema."""
        data = {
            "name": "Test Campaign",
            "advertiser_id": "00000000-0000-0000-0000-000000000002",
            "status": "active",
        }
        schema = CampaignCreate(**data)

        assert schema.name == "Test Campaign"
        assert schema.advertiser_id == UUID("00000000-0000-0000-0000-000000000002")
        assert schema.status == "active"

    def test_insertion_order_create_schema(self):
        """Test InsertionOrderCreate schema."""
        data = {
            "campaign_id": "00000000-0000-0000-0000-000000000003",
            "file_path": "ios/test.pdf",
            "file_type": "application/pdf",
            "processing_status": "uploaded",
            "extracted_data": {"campaign_name": "Test Campaign", "budget": 10000},
        }
        schema = InsertionOrderCreate(**data)

        assert schema.campaign_id == UUID("00000000-0000-0000-0000-000000000003")
        assert schema.file_path == "ios/test.pdf"
        assert schema.file_type == "application/pdf"
        assert schema.processing_status == "uploaded"
        assert schema.extracted_data["campaign_name"] == "Test Campaign"
        assert schema.extracted_data["budget"] == 10000

    def test_insertion_order_update_schema(self):
        """Test InsertionOrderUpdate schema."""
        data = {
            "processing_status": "processed",
            "extracted_data": {"campaign_name": "Updated Campaign", "budget": 15000},
        }
        schema = InsertionOrderUpdate(**data)

        assert schema.processing_status == "processed"
        assert schema.extracted_data["campaign_name"] == "Updated Campaign"
        assert schema.extracted_data["budget"] == 15000

    def test_insertion_order_extracted_data_schema(self):
        """Test InsertionOrderExtractedData schema."""
        data = {
            "campaign_name": "Test Campaign",
            "advertiser_name": "Test Advertiser",
            "line_items": [
                {"name": "Banner Ad", "format": "300x250", "quantity": 5},
                {"name": "Video Ad", "format": "1920x1080", "quantity": 2},
            ],
        }
        schema = InsertionOrderExtractedData(**data)

        assert schema.campaign_name == "Test Campaign"
        assert schema.advertiser_name == "Test Advertiser"
        assert len(schema.line_items) == 2
        assert schema.line_items[0]["name"] == "Banner Ad"
        assert schema.line_items[0]["format"] == "300x250"
        assert schema.line_items[0]["quantity"] == 5
