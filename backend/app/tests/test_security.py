"""
Tests for security module.
"""

from datetime import datetime, timedelta
from unittest.mock import patch

from jose import jwt

# Import directly from the module file
import app.core.security as security


class TestSecurity:
    """Test security functions."""

    @patch("app.core.security.settings")
    def test_create_access_token(self, mock_settings):
        """Test creating an access token."""
        # Setup mock settings
        mock_settings.APP_SECRET_KEY = "test_secret_key"

        # Create a token with test data
        data = {"sub": "<EMAIL>", "role": "user"}
        token = security.create_access_token(data)

        # Verify that the token is a string
        assert isinstance(token, str)

        # Decode the token and verify the claims
        payload = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        assert payload["sub"] == "<EMAIL>"
        assert payload["role"] == "user"
        assert "exp" in payload

    @patch("app.core.security.settings")
    def test_create_access_token_with_expiration(self, mock_settings):
        """Test creating an access token with a custom expiration."""
        # Setup mock settings
        mock_settings.APP_SECRET_KEY = "test_secret_key"

        # Create a token with test data and custom expiration
        data = {"sub": "<EMAIL>"}
        expires_delta = timedelta(minutes=15)
        token = security.create_access_token(data, expires_delta=expires_delta)

        # Verify that the token is a string
        assert isinstance(token, str)

        # Decode the token and verify the expiration
        payload = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        exp_time = datetime.fromtimestamp(payload["exp"])
        now = datetime.utcnow()
        # The expiration should be about 15 minutes in the future
        assert (exp_time - now).total_seconds() > 14 * 60
        assert (exp_time - now).total_seconds() < 16 * 60
