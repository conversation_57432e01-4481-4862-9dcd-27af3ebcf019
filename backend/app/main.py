"""
YCA Collector - Main Application

This module initializes and configures the FastAPI application.
"""

import logging

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api.api import api_router
from app.core.config import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI application
app = FastAPI(
    title="YCA Collector API",
    description="API for managing creative assets and insertion orders",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(api_router)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "ok",
        "environment": settings.APP_ENV,
        "debug": settings.APP_DEBUG,
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"},
    )


# Application startup event
@app.on_event("startup")
async def startup_event():
    """Run application startup tasks."""
    logger.info("Starting YCA Collector application...")

    # Connect to database using SQLAlchemy service
    from app.services.database_service import get_db_service

    db_service = get_db_service()
    db_service.connect()
    # Note: create_tables() is commented out to avoid accidental data loss on existing DBs.
    # db_service.create_tables()

    # Test Supabase connection
    from app.services.supabase_service import supabase_service

    connection_ok = await supabase_service.test_connection()
    if connection_ok:
        logger.info("✅ Supabase connection established")  # Kept current message
    else:
        logger.error("❌ Failed to connect to Supabase")  # Kept current message


# Initialize settings after routes to ensure they are available globally if needed by settings logic
settings = get_settings()


# Application shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Run application shutdown tasks."""
    logger.info("Shutting down YCA Collector application...")
    # Clean up resources if needed
