"""
Database service for handling database operations with SQLAlchemy.
"""

import logging
from typing import Any, TypeVar
from uuid import UUID

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, scoped_session, sessionmaker

from app.core.config import get_settings
from app.models.database_models import Base

settings = get_settings()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type variables
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class DatabaseService:
    """Service for handling database operations with SQLAlchemy."""

    def __init__(self, database_url: str):
        """Initialize the database service.

        Args:
            database_url: Database connection URL
        """
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None

    def connect(self):
        """Create database engine and session factory."""
        try:
            self.engine = create_engine(
                self.database_url,
                pool_pre_ping=True,
                pool_recycle=300,
                pool_size=5,
                max_overflow=10,
            )
            self.SessionLocal = scoped_session(
                sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            )
            logger.info("Database connection established successfully.")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to the database: {str(e)}")
            raise

    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully.")
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
            raise

    def get_db(self) -> Session:
        """Get a database session.

        Yields:
            Session: A database session
        """
        if not self.SessionLocal:
            raise RuntimeError(
                "Database connection not established. Call connect() first."
            )

        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

    def execute_raw_sql(self, sql: str, params: dict | None = None) -> list[dict]:
        """Execute raw SQL query.

        Args:
            sql: SQL query string
            params: Query parameters

        Returns:
            List of result rows as dictionaries
        """
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(sql), params or {})
                return [dict(row) for row in result.mappings()]
        except SQLAlchemyError as e:
            logger.error(f"Error executing raw SQL: {str(e)}")
            raise

    def get(
        self, db: Session, model: type[ModelType], id: str | UUID
    ) -> ModelType | None:
        """Get a single record by ID.

        Args:
            db: Database session
            model: SQLAlchemy model class
            id: Record ID

        Returns:
            The model instance or None if not found
        """
        try:
            return db.query(model).filter(model.id == id).first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting {model.__name__} with ID {id}: {str(e)}")
            raise

    def list(
        self,
        db: Session,
        model: type[ModelType],
        skip: int = 0,
        limit: int = 100,
        **filters,
    ) -> list[ModelType]:
        """List records with optional filtering and pagination.

        Args:
            db: Database session
            model: SQLAlchemy model class
            skip: Number of records to skip
            limit: Maximum number of records to return
            **filters: Filter conditions as keyword arguments

        Returns:
            List of model instances
        """
        try:
            query = db.query(model)

            # Apply filters
            for field, value in filters.items():
                if hasattr(model, field) and value is not None:
                    query = query.filter(getattr(model, field) == value)

            return query.offset(skip).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Error listing {model.__name__} records: {str(e)}")
            raise

    def create(
        self, db: Session, model: type[ModelType], obj_in: CreateSchemaType
    ) -> ModelType:
        """Create a new record.

        Args:
            db: Database session
            model: SQLAlchemy model class
            obj_in: Pydantic model with data to create

        Returns:
            The created model instance
        """
        try:
            db_obj = model(**obj_in.model_dump())
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error creating {model.__name__}: {str(e)}")
            raise

    def update(
        self,
        db: Session,
        db_obj: ModelType,
        obj_in: UpdateSchemaType | dict[str, Any],
    ) -> ModelType:
        """Update an existing record.

        Args:
            db: Database session
            db_obj: The database object to update
            obj_in: Pydantic model or dict with data to update

        Returns:
            The updated model instance
        """
        try:
            obj_data = (
                obj_in.model_dump(exclude_unset=True)
                if not isinstance(obj_in, dict)
                else obj_in
            )

            for field, value in obj_data.items():
                setattr(db_obj, field, value)

            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error updating {db_obj.__class__.__name__}: {str(e)}")
            raise

    def delete(self, db: Session, db_obj: ModelType) -> bool:
        """Delete a record.

        Args:
            db: Database session
            db_obj: The database object to delete

        Returns:
            True if deletion was successful
        """
        try:
            db.delete(db_obj)
            db.commit()
            return True
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Error deleting {db_obj.__class__.__name__}: {str(e)}")
            raise


# Initialize database service
def _build_postgres_url() -> str:
    """Build PostgreSQL connection URL from Supabase URL."""
    import re
    from urllib.parse import quote_plus

    # Extract project reference from Supabase URL
    url_match = re.search(r"https://([^.]+)\.supabase\.co", settings.SUPABASE_URL)
    if not url_match:
        raise ValueError(f"Invalid Supabase URL format: {settings.SUPABASE_URL}")

    project_ref = url_match.group(1)

    # URL encode the password to handle special characters
    encoded_password = quote_plus(settings.SUPABASE_DB_PASSWORD)

    return f"postgresql://postgres:{encoded_password}@db.{project_ref}.supabase.co:5432/postgres"


try:
    postgres_url = _build_postgres_url()
    _db_instance = DatabaseService(database_url=postgres_url)
except Exception as e:
    logger.warning(f"Failed to build database connection: {e}")
    # Create dummy instance that will fail gracefully
    _db_instance = DatabaseService(database_url="postgresql://dummy")


def get_db_service() -> DatabaseService:
    """Dependency that provides the DatabaseService instance."""
    return _db_instance


class DatabaseDependency:
    """Database dependency class for FastAPI endpoints."""

    def __init__(self):
        self.db_service = get_db_service()

    def get_db(self):
        """Get a database session."""
        return self.db_service.get_db()


# Create the db dependency instance
db = DatabaseDependency()
