"""
Enhanced IO Service with LangGraph Integration.

This service integrates the original IO processing functionality
with the new LangGraph workflow system for better orchestration
and monitoring.
"""

import logging
from typing import Any

from fastapi import HTTPException, UploadFile, status

from app.services.database_service import DatabaseService
from app.services.file_service import FileService
from app.workflows.manager import WorkflowManager, WorkflowType

logger = logging.getLogger(__name__)


class LangGraphIOService:
    """Enhanced IO Service using LangGraph workflows."""

    def __init__(
        self,
        db_service: DatabaseService,
        file_service: FileService,
        workflow_manager: WorkflowManager,
    ):
        self.db_service = db_service
        self.file_service = file_service
        self.workflow_manager = workflow_manager

    async def process_io_upload(
        self,
        file: UploadFile,
        advertiser_id: str | None = None,
        campaign_name: str | None = None,
    ) -> dict[str, Any]:
        """
        Process IO upload using LangGraph workflow orchestration.

        This method prepares the input data and delegates the actual
        processing to the LangGraph IO processing workflow.
        """
        try:
            # Validate file size (max 50MB)
            max_file_size = 50 * 1024 * 1024  # 50MB
            if hasattr(file, "size") and file.size and file.size > max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"File too large. Maximum size allowed is {max_file_size // (1024 * 1024)}MB",
                )

            # Read file content with streaming for large files
            file_content = await self._read_file_safely(file, max_file_size)

            # Prepare workflow input data
            workflow_input = {
                "file": {
                    "filename": file.filename or "unknown",
                    "content_type": file.content_type or "application/octet-stream",
                    "size": len(file_content),
                    # Note: In real implementation, we'd pass file content or stream
                    "content": file_content,
                },
                "advertiser_id": advertiser_id,
                "campaign_name": campaign_name or f"Campaign for {file.filename}",
                "processing_options": {
                    "extract_line_items": True,
                    "validate_data": True,
                    "store_in_database": True,
                },
            }

            # Reset file pointer for potential future reads
            await file.seek(0)

            # Execute the LangGraph workflow
            workflow_id = await self.workflow_manager.execute_workflow(
                WorkflowType.IO_PROCESSING, workflow_input
            )

            logger.info(f"Started LangGraph IO processing workflow: {workflow_id}")

            return {
                "workflow_id": workflow_id,
                "status": "processing_started",
                "message": "IO document processing initiated using LangGraph workflow",
                "processing_type": "langgraph_orchestrated",
            }

        except Exception as e:
            logger.error(f"Error starting LangGraph IO workflow: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start IO processing workflow: {str(e)}",
            ) from e

    async def get_workflow_status(self, workflow_id: str) -> dict[str, Any]:
        """Get the status of an IO processing workflow."""
        workflow_status = self.workflow_manager.get_workflow_status(workflow_id)

        if not workflow_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow {workflow_id} not found",
            )

        return workflow_status

    async def list_workflows(self, status_filter: str | None = None) -> dict[str, Any]:
        """List all IO processing workflows."""
        from app.workflows.manager import WorkflowStatus

        status_enum = None
        if status_filter:
            try:
                status_enum = WorkflowStatus(status_filter.lower())
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}",
                ) from e

        workflows = self.workflow_manager.list_workflows(status_enum)

        # Filter for IO processing workflows only
        io_workflows = [
            w
            for w in workflows
            if w["workflow_type"] == WorkflowType.IO_PROCESSING.value
        ]

        return {
            "workflows": io_workflows,
            "total_count": len(io_workflows),
            "filter_applied": status_filter,
        }

    async def retry_workflow(self, workflow_id: str) -> dict[str, Any]:
        """Retry a failed workflow."""
        new_workflow_id = await self.workflow_manager.retry_workflow(workflow_id)

        if not new_workflow_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot retry workflow {workflow_id}. It may not exist or not be in a failed state.",
            )

        return {
            "original_workflow_id": workflow_id,
            "new_workflow_id": new_workflow_id,
            "status": "retry_initiated",
        }

    async def cancel_workflow(self, workflow_id: str) -> dict[str, Any]:
        """Cancel a running workflow."""
        cancelled = await self.workflow_manager.cancel_workflow(workflow_id)

        if not cancelled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel workflow {workflow_id}. It may not exist or not be running.",
            )

        return {"workflow_id": workflow_id, "status": "cancelled"}

    def get_workflow_metrics(self) -> dict[str, Any]:
        """Get metrics about workflow executions."""
        metrics = self.workflow_manager.get_execution_metrics()

        # Add IO-specific metrics
        io_metrics = {
            "io_processing_workflows": metrics,
            "service_type": "langgraph_enhanced",
            "features": [
                "async_processing",
                "workflow_orchestration",
                "error_recovery",
                "status_monitoring",
                "retry_capability",
            ],
        }

        return io_metrics

    async def _read_file_safely(self, file: UploadFile, max_size: int) -> bytes:
        """
        Safely read file content with streaming support for large files.

        Args:
            file: The uploaded file
            max_size: Maximum allowed file size in bytes

        Returns:
            File content as bytes

        Raises:
            HTTPException: If file is too large
        """
        content = b""
        chunk_size = 8192  # 8KB chunks

        try:
            while True:
                chunk = await file.read(chunk_size)
                if not chunk:
                    break

                content += chunk

                # Check size after each chunk to prevent memory exhaustion
                if len(content) > max_size:
                    raise HTTPException(
                        status_code=413,
                        detail=f"File too large. Maximum size allowed is {max_size // (1024 * 1024)}MB",
                    )

            return content

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            logger.error(f"Error reading file: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error reading file content",
            ) from e


# Factory function for dependency injection
def create_langgraph_io_service(
    db_service: DatabaseService,
    file_service: FileService,
    workflow_manager: WorkflowManager,
) -> LangGraphIOService:
    """Create a LangGraph-enhanced IO service instance."""
    return LangGraphIOService(db_service, file_service, workflow_manager)
