import logging
import os
from typing import Any
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile, status  # Added status

# <PERSON><PERSON>hain imports
from langchain_community.document_loaders import (
    Docx2txtLoader,
    PyPDFLoader,
    UnstructuredExcelLoader,
)
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_google_genai import ChatGoogleG<PERSON>ative<PERSON><PERSON>

# from langchain_core.runnables import RunnableSequence # This was in original diff but not in current code, leaving out.
from app.core.config import get_settings  # Changed from app.config
from app.core.utils import generate_uuid, get_file_extension
from app.models.database_models import (  # LineItem removed as per diff context and not being used
    Advertiser,
    Campaign,
    InsertionOrder,
    Publisher,
)
from app.models.schemas import (
    AdvertiserCreate,  # Added
    CampaignCreate,  # Added
    InsertionOrderCreate,
    InsertionOrderExtractedData,
    InsertionOrderUpdate,
    PublisherCreate,  # Added
)
from app.services.database_service import DatabaseService
from app.services.file_service import FileService

settings = get_settings()  # Added

logger = logging.getLogger(__name__)


class IOService:
    def __init__(self, db_service: DatabaseService, file_service: FileService):
        self.db_service = db_service
        self.file_service = file_service

    async def process_io_upload(
        self,
        file: UploadFile,
        advertiser_id: str
        | None = None,  # Diff had Optional[str], Python 3.10+ can use | None
        campaign_name: str | None = None,  # Diff had Optional[str]
    ) -> dict[str, Any]:  # Diff had Dict[str, Any]
        """
        Handles the upload and initial processing of an IO document.
        This is the core Phase 1 functionality.
        """
        workflow_id = generate_uuid()
        filename = file.filename if file.filename else ""
        file_extension = get_file_extension(filename)

        storage_path = f"ios/{workflow_id}.{file_extension}"

        try:
            # 1. Upload file to Supabase Storage
            file_url = await self.file_service.upload_file(file, storage_path)
            logger.info(f"File uploaded to Supabase Storage: {file_url}")

            # 2. Find or create the campaign for this IO
            campaign_db_obj = None
            if advertiser_id:  # Ensure advertiser_id is provided to proceed
                with next(self.db_service.get_db()) as db:
                    advertiser = self.db_service.get(
                        db, Advertiser, UUID(advertiser_id)
                    )
                    if not advertiser:
                        logger.warning(
                            f"Advertiser with ID {advertiser_id} not found. Creating dummy data."
                        )

                        default_publisher_list = self.db_service.list(
                            db, Publisher, name="Default Publisher"
                        )
                        if not default_publisher_list:
                            publisher_create_schema = PublisherCreate(
                                name="Default Publisher", email="<EMAIL>"
                            )
                            publisher = self.db_service.create(
                                db, Publisher, publisher_create_schema
                            )
                        else:
                            publisher = default_publisher_list[0]

                        advertiser_create_schema = AdvertiserCreate(
                            name="Default Advertiser",
                            email="<EMAIL>",
                            publisher_id=publisher.id,
                        )
                        advertiser = self.db_service.create(
                            db, Advertiser, advertiser_create_schema
                        )
                        logger.warning(
                            f"Dummy Advertiser created with ID: {advertiser.id}"
                        )

                    campaign_create_data = {
                        "name": campaign_name
                        if campaign_name
                        else f"Campaign for {file.filename}",
                        "advertiser_id": advertiser.id,  # Use the found or created advertiser's ID
                        "status": "active",
                    }
                    campaign_create_schema = CampaignCreate(**campaign_create_data)
                    campaign_db_obj = self.db_service.create(
                        db, Campaign, campaign_create_schema
                    )
                    logger.info(f"Campaign created with ID: {campaign_db_obj.id}")

            if not campaign_db_obj:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Could not determine campaign for IO.",
                )

            insertion_order_data = InsertionOrderCreate(
                campaign_id=campaign_db_obj.id,  # Assign the actual campaign_id
                file_path=file_url,
                file_type=file.content_type,
                processing_status="uploaded",
                extracted_data={},
            )

            with next(self.db_service.get_db()) as db:
                io_record = self.db_service.create(
                    db, InsertionOrder, insertion_order_data
                )
                logger.info(f"InsertionOrder record created with ID: {io_record.id}")

            io_content_bytes = await self.file_service.download_file(storage_path)
            content_type = (
                file.content_type if file.content_type else "application/octet-stream"
            )
            extracted_text = await self._extract_text_from_io(
                io_content_bytes, content_type
            )
            logger.info(
                f"Extracted text from IO (first 200 chars): {extracted_text[:200]}"
            )

            extracted_data_pydantic = await self._extract_structured_data_with_ai(
                extracted_text
            )
            extracted_data_dict = extracted_data_pydantic.model_dump()
            logger.info(f"Extracted structured data: {extracted_data_dict}")

            with next(self.db_service.get_db()) as db:
                updated_io_record = self.db_service.update(
                    db,
                    io_record,
                    InsertionOrderUpdate(
                        extracted_data=extracted_data_dict,
                        processing_status="processed",
                    ),
                )
                logger.info(
                    f"InsertionOrder record updated with extracted data: {updated_io_record.id}"
                )

            return {
                "workflow_id": workflow_id,
                "insertion_order_id": str(updated_io_record.id),
                "status": "processing_complete",
                "extracted_data": extracted_data_dict,
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in process_io_upload: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,  # Corrected status code
                detail=f"Internal server error during IO processing: {str(e)}",
            ) from e

    async def _extract_text_from_io(
        self, file_content: bytes, content_type: str
    ) -> str:
        temp_file_path = f"/tmp/{generate_uuid()}"
        try:
            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            loader: Any = None
            if content_type == "application/pdf":
                loader = PyPDFLoader(temp_file_path)
            elif (
                content_type
                == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            ):
                loader = Docx2txtLoader(temp_file_path)
            elif (
                content_type
                == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ):
                loader = UnstructuredExcelLoader(temp_file_path)
            else:
                raise ValueError(
                    f"Unsupported content type for text extraction: {content_type}"
                )

            docs = loader.load()
            return "\n".join([doc.page_content for doc in docs])
        finally:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    async def _extract_structured_data_with_ai(
        self, text_content: str
    ) -> InsertionOrderExtractedData:
        parser = PydanticOutputParser(pydantic_object=InsertionOrderExtractedData)

        prompt = PromptTemplate(
            template="Answer the user query.\n{format_instructions}\n{query}\n",
            input_variables=["query"],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

        model = ChatGoogleGenerativeAI(
            model="gemini-pro", google_api_key=settings.GEMINI_API_KEY
        )

        chain = prompt | model | parser

        try:
            extracted_data = await chain.ainvoke({"query": text_content})
            return extracted_data
        except Exception as e:
            logger.error(
                f"Error extracting structured data with AI: {e}", exc_info=True
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI extraction failed: {str(e)}",  # Corrected status code
            ) from e

    async def get_workflow_status(
        self, workflow_id: str
    ) -> dict[str, Any]:  # Diff had Dict
        with next(self.db_service.get_db()) as db:
            io_record = self.db_service.get(db, InsertionOrder, UUID(workflow_id))
            if not io_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Workflow not found"
                )  # Corrected status code
            return {
                "workflow_id": workflow_id,
                "status": io_record.processing_status,
                "extracted_data": io_record.extracted_data,
            }

    async def list_workflows(
        self,
        limit: int = 10,
        offset: int = 0,  # Current code had limit 10, diff implies 100. Sticking to current as it's not in explicit diff.
    ) -> list[dict[str, Any]]:  # Diff had List
        with next(self.db_service.get_db()) as db:
            ios = self.db_service.list(db, InsertionOrder, skip=offset, limit=limit)
            return [
                {
                    "workflow_id": str(io.id),
                    "file_path": io.file_path,
                    "processing_status": io.processing_status,
                    "created_at": io.created_at.isoformat(),
                }
                for io in ios
            ]

    async def retry_workflow(self, workflow_id: str) -> dict[str, Any]:  # Diff had Dict
        with next(self.db_service.get_db()) as db:
            io_record = self.db_service.get(db, InsertionOrder, UUID(workflow_id))
            if not io_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Workflow not found"
                )  # Corrected status code

            updated_io = self.db_service.update(
                db, io_record, InsertionOrderUpdate(processing_status="retrying")
            )
            return {
                "workflow_id": str(updated_io.id),
                "status": updated_io.processing_status,
                "message": "Workflow retry initiated (placeholder)",
            }
