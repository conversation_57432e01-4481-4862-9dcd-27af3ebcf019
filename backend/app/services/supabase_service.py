"""
Supabase database service for YCA Collector.
Handles all database operations with the existing Supabase database.
"""

import logging
from typing import Any

from supabase import Client, create_client

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class SupabaseService:
    """Service for interacting with Supabase database."""

    def __init__(self):
        """Initialize Supabase client."""
        self.client: Client = create_client(
            settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY
        )
        logger.info("Supabase client initialized")

    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            # Simple query to test connection
            self.client.table("publishers").select("count", count="exact").execute()
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False

    # ===== AI PERSONAS =====
    async def get_ai_personas(self) -> list[dict[str, Any]]:
        """Get all active AI personas from database."""
        try:
            result = (
                self.client.table("ai_personas")
                .select("*")
                .eq("is_active", True)
                .order("name")
                .execute()
            )

            logger.info(f"Retrieved {len(result.data)} AI personas")
            return result.data
        except Exception as e:
            logger.error(f"Error fetching AI personas: {e}")
            return []

    async def get_persona_by_name(self, name: str) -> dict[str, Any] | None:
        """Get specific AI persona by name."""
        try:
            result = (
                self.client.table("ai_personas")
                .select("*")
                .eq("name", name)
                .eq("is_active", True)
                .single()
                .execute()
            )

            return result.data if result.data else None
        except Exception as e:
            logger.error(f"Error fetching persona {name}: {e}")
            return None

    # ===== PUBLISHERS & ADVERTISERS =====
    async def get_publishers(self) -> list[dict[str, Any]]:
        """Get all publishers."""
        try:
            result = self.client.table("publishers").select("*").order("name").execute()

            return result.data
        except Exception as e:
            logger.error(f"Error fetching publishers: {e}")
            return []

    async def get_advertiser_by_id(self, advertiser_id: str) -> dict[str, Any] | None:
        """Get advertiser by ID."""
        try:
            result = (
                self.client.table("advertisers")
                .select("*")
                .eq("id", advertiser_id)
                .single()
                .execute()
            )

            return result.data if result.data else None
        except Exception as e:
            logger.error(f"Error fetching advertiser {advertiser_id}: {e}")
            return None

    # ===== INSERTION ORDERS =====
    async def create_insertion_order(
        self, io_data: dict[str, Any]
    ) -> dict[str, Any] | None:
        """Create new insertion order record."""
        try:
            result = self.client.table("insertion_orders").insert(io_data).execute()

            if result.data:
                logger.info(f"Created insertion order: {result.data[0]['id']}")
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error creating insertion order: {e}")
            return None

    async def update_insertion_order(
        self, io_id: str, update_data: dict[str, Any]
    ) -> dict[str, Any] | None:
        """Update insertion order."""
        try:
            # Add updated_at timestamp
            update_data["updated_at"] = "now()"

            result = (
                self.client.table("insertion_orders")
                .update(update_data)
                .eq("id", io_id)
                .execute()
            )

            if result.data:
                logger.info(f"Updated insertion order: {io_id}")
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error updating insertion order {io_id}: {e}")
            return None

    async def get_insertion_order(self, io_id: str) -> dict[str, Any] | None:
        """Get insertion order by ID."""
        try:
            result = (
                self.client.table("insertion_orders")
                .select("*")
                .eq("id", io_id)
                .single()
                .execute()
            )

            return result.data if result.data else None
        except Exception as e:
            logger.error(f"Error fetching insertion order {io_id}: {e}")
            return None

    # ===== LINE ITEMS =====
    async def create_line_items(
        self, line_items_data: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """Create multiple line items."""
        try:
            result = self.client.table("line_items").insert(line_items_data).execute()

            if result.data:
                logger.info(f"Created {len(result.data)} line items")
                return result.data
            return []
        except Exception as e:
            logger.error(f"Error creating line items: {e}")
            return []

    async def get_line_items_by_io(self, io_id: str) -> list[dict[str, Any]]:
        """Get all line items for an insertion order."""
        try:
            result = (
                self.client.table("line_items")
                .select("*")
                .eq("io_id", io_id)
                .order("line_item_number")
                .execute()
            )

            return result.data
        except Exception as e:
            logger.error(f"Error fetching line items for IO {io_id}: {e}")
            return []

    # ===== WORKFLOW LOGS =====
    async def log_workflow_step(
        self, log_data: dict[str, Any]
    ) -> dict[str, Any] | None:
        """Log workflow execution step."""
        try:
            result = self.client.table("workflow_logs").insert(log_data).execute()

            if result.data:
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error logging workflow step: {e}")
            return None

    # ===== EMAIL COMMUNICATIONS =====
    async def log_email_communication(
        self, email_data: dict[str, Any]
    ) -> dict[str, Any] | None:
        """Log email communication."""
        try:
            result = (
                self.client.table("email_communications").insert(email_data).execute()
            )

            if result.data:
                logger.info(f"Logged email communication: {result.data[0]['id']}")
                return result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error logging email communication: {e}")
            return None

    # ===== SYSTEM SETTINGS =====
    async def get_system_setting(self, setting_key: str) -> Any | None:
        """Get system setting value."""
        try:
            result = (
                self.client.table("system_settings")
                .select("setting_value")
                .eq("setting_key", setting_key)
                .eq("is_active", True)
                .single()
                .execute()
            )

            if result.data:
                return result.data["setting_value"]
            return None
        except Exception as e:
            logger.error(f"Error fetching setting {setting_key}: {e}")
            return None


# Global instance
supabase_service = SupabaseService()
