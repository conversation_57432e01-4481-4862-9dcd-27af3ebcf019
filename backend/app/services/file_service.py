"""
File handling service for managing file uploads and storage.
"""

import logging
import os
import uuid
from datetime import datetime

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile, status
from supabase import Client, create_client

from app.core.config import get_settings
from app.core.utils import sanitize_filename as core_sanitize_filename

settings = get_settings()

logger = logging.getLogger(__name__)


class FileService:
    """Service for handling file operations with Supabase Storage."""

    def __init__(self, bucket_name: str = "creative-collector"):
        """Initialize the Supabase File Service.

        Args:
            bucket_name: The name of the Supabase Storage bucket.
        """
        self.bucket_name = bucket_name
        self.supabase: Client = create_client(
            settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY
        )
        self.allowed_extensions = {
            # Document formats
            ".pdf",
            ".doc",
            ".docx",
            ".xls",
            ".xlsx",
            ".ppt",
            ".pptx",
            ".txt",
            ".csv",
            # Image formats
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".tiff",
            ".webp",
            ".svg",
            # Video formats
            ".mp4",
            ".mov",
            ".avi",
            ".wmv",
            ".flv",
            ".mkv",
            # Audio formats
            ".mp3",
            ".wav",
            ".ogg",
            ".m4a",
        }

    async def upload_file(
        self,
        file: UploadFile,
        path: str,  # e.g., "ios/advertiser_id/campaign_id/filename.pdf"
        file_options: dict | None = None,
    ) -> str:
        """Upload a file to Supabase Storage.

        Args:
            file: The FastAPI UploadFile object.
            path: The full path including filename within the Supabase bucket.
            file_options: Optional dictionary for Supabase upload options (e.g., {'upsert': True}).

        Returns:
            The public URL of the uploaded file.

        Raises:
            HTTPException: If the file type is not allowed or upload fails.
        """
        filename = file.filename if file.filename else ""
        if not self.is_allowed_file(filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported file type: {self.get_file_extension(filename)}. Supported: {', '.join(self.allowed_extensions)}",
            )

        try:
            # Read file content
            contents = await file.read()

            # Upload to Supabase Storage
            response = self.supabase.storage.from_(self.bucket_name).upload(
                path=path,
                file=contents,
                file_options=file_options or {"content-type": file.content_type},
            )

            if response.status_code == 200:
                # Get public URL
                public_url_response = self.supabase.storage.from_(
                    self.bucket_name
                ).get_public_url(path)
                return public_url_response
            else:
                logger.error(f"Supabase upload error: {response.json()}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to upload file to storage: {response.json().get('message', 'Unknown error')}",
                )
        except Exception as e:
            logger.error(f"Error uploading file to Supabase: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}",
            ) from e

    async def download_file(self, path: str) -> bytes:
        """Download a file from Supabase Storage.

        Args:
            path: The full path including filename within the Supabase bucket.

        Returns:
            The content of the file as bytes.

        Raises:
            HTTPException: If download fails or file not found.
        """
        try:
            response = self.supabase.storage.from_(self.bucket_name).download(path)
            return response
        except Exception as e:
            logger.error(f"Error downloading file from Supabase: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to download file: {str(e)}",
            ) from e

    async def delete_file(self, paths: str | list[str]) -> dict:
        """Delete one or more files from Supabase Storage.

        Args:
            paths: A single path string or a list of path strings to delete.

        Returns:
            A dictionary with deletion status.

        Raises:
            HTTPException: If deletion fails.
        """
        if isinstance(paths, str):
            paths = [paths]

        try:
            response = self.supabase.storage.from_(self.bucket_name).remove(paths)
            if response.status_code == 200:
                return {
                    "message": "Files deleted successfully",
                    "deleted_files": response.json(),
                }
            else:
                logger.error(f"Supabase delete error: {response.json()}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Failed to delete files from storage: {response.json().get('message', 'Unknown error')}",
                )
        except Exception as e:
            logger.error(f"Error deleting file from Supabase: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete file: {str(e)}",
            ) from e

    def get_file_extension(self, filename: str) -> str:
        """Get file extension from filename."""
        return os.path.splitext(filename.lower())[1]

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage."""
        # Use the comprehensive sanitization from core utils
        return core_sanitize_filename(filename)

    def is_allowed_file(self, filename: str) -> bool:
        """Check if a file has an allowed extension.

        Args:
            filename: The name of the file to check

        Returns:
            True if the file extension is allowed, False otherwise
        """
        ext = self.get_file_extension(filename)
        return ext in self.allowed_extensions

    async def save_file(self, file: UploadFile, advertiser_id: str) -> dict:
        """Save file and return metadata. Compatible with IO endpoint.

        Args:
            file: UploadFile to save
            advertiser_id: ID of advertiser for organizing files

        Returns:
            Dictionary with file metadata including storage_path
        """
        try:
            # Generate unique filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            safe_filename = self.sanitize_filename(file.filename or "unknown")

            # Create storage path: ios/advertiser_id/timestamp_uniqueid_filename
            storage_path = (
                f"ios/{advertiser_id}/{timestamp}_{unique_id}_{safe_filename}"
            )

            # Upload file to Supabase Storage
            public_url = await self.upload_file(file, storage_path)

            return {
                "file_id": unique_id,
                "filename": safe_filename,
                "storage_path": storage_path,
                "public_url": public_url,
                "content_type": file.content_type,
                "size": file.size,
            }

        except Exception as e:
            logger.error(f"Error saving file: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save file: {str(e)}",
            ) from e


# Initialize the file service
_file_service_instance = FileService()


def get_file_service() -> FileService:
    """Dependency that provides the FileService instance."""
    return _file_service_instance


# Global file service instance for direct imports
file_service = _file_service_instance
