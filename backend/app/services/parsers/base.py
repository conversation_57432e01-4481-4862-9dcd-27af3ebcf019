"""
Base classes for document parsers.
"""

from abc import ABC, abstractmethod
from typing import Any


class ParsedDocument:
    """
    Represents a parsed document with extracted content and metadata.
    """

    def __init__(
        self,
        content: str,
        metadata: dict[str, Any] | None = None,
        file_type: str | None = None,
        file_name: str | None = None,
    ):
        self.content = content
        self.metadata = metadata or {}
        self.file_type = file_type
        self.file_name = file_name

    def __str__(self) -> str:
        return f"ParsedDocument(file_name={self.file_name}, file_type={self.file_type})"


class BaseDocumentParser(ABC):
    """
    Abstract base class for document parsers.
    """

    @abstractmethod
    def parse(self, file_content: bytes) -> ParsedDocument:
        """
        Parse the document content and return a ParsedDocument.

        Args:
            file_content: Raw file content as bytes

        Returns:
            ParsedDocument: Parsed document with content and metadata
        """
        pass

    @property
    @abstractmethod
    def supported_mime_types(self) -> list[str]:
        """
        List of MIME types supported by this parser.
        """
        pass
