"""
DOCX document parser implementation.
"""

import io
from typing import Any

import docx2txt

from app.services.parsers.base import BaseDocument<PERSON>arser, ParsedDocument


class DocxParser(BaseDocumentParser):
    """
    Parser for DOCX documents.
    """

    def parse(self, file_content: bytes) -> ParsedDocument:
        """
        Parse DOCX content and extract text and metadata.

        Args:
            file_content: Raw DOCX content as bytes

        Returns:
            ParsedDocument: Parsed document with content and metadata
        """
        # Extract text using docx2txt
        text = docx2txt.process(io.BytesIO(file_content))

        # Basic metadata
        metadata: dict[str, Any] = {"format": "DOCX", "size_bytes": len(file_content)}

        return ParsedDocument(
            content=text,
            metadata=metadata,
            file_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        )

    @property
    def supported_mime_types(self) -> list[str]:
        """
        List of MIME types supported by this parser.
        """
        return [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
        ]
