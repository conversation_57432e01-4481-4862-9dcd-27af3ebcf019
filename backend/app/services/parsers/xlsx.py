"""
XLSX document parser implementation.
"""

import io
from typing import Any

import openpyxl

from app.services.parsers.base import BaseDocumentParser, ParsedDocument


class XlsxParser(BaseDocumentParser):
    """
    Parser for XLSX documents.
    """

    def parse(self, file_content: bytes) -> ParsedDocument:
        """
        Parse XLSX content and extract text and metadata.

        Args:
            file_content: Raw XLSX content as bytes

        Returns:
            ParsedDocument: Parsed document with content and metadata
        """
        # Load workbook
        wb = openpyxl.load_workbook(io.BytesIO(file_content), data_only=True)

        # Extract text from all sheets
        text = []
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]
            sheet_text = f"Sheet: {sheet_name}\n"

            # Extract cell values as text
            for row in sheet.iter_rows():
                row_values = [
                    str(cell.value) if cell.value is not None else "" for cell in row
                ]
                sheet_text += "\t".join(row_values) + "\n"

            text.append(sheet_text)

        # Basic metadata
        metadata: dict[str, Any] = {
            "format": "XLSX",
            "sheet_count": len(wb.sheetnames),
            "sheet_names": wb.sheetnames,
            "size_bytes": len(file_content),
        }

        return ParsedDocument(
            content="\n\n".join(text),
            metadata=metadata,
            file_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

    @property
    def supported_mime_types(self) -> list[str]:
        """
        List of MIME types supported by this parser.
        """
        return [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
        ]
