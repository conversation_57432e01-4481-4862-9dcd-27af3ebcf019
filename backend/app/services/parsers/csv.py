"""
CSV document parser implementation.
"""

import csv
import io
from typing import Any

from app.services.parsers.base import BaseDocumentParser, ParsedDocument


class CSVParser(BaseDocumentParser):
    """
    Parser for CSV documents.
    """

    def parse(self, file_content: bytes) -> ParsedDocument:
        """
        Parse CSV content and extract text and metadata.

        Args:
            file_content: Raw CSV content as bytes

        Returns:
            ParsedDocument: Parsed document with content and metadata
        """
        # Convert bytes to text
        text_content = file_content.decode("utf-8", errors="replace")

        # Parse CSV
        csv_file = io.StringIO(text_content)
        csv_reader = csv.reader(csv_file)

        # Extract rows
        rows = list(csv_reader)

        # Convert to text representation
        text = "\n".join(["\t".join(row) for row in rows])

        # Basic metadata
        metadata: dict[str, Any] = {
            "format": "CSV",
            "row_count": len(rows),
            "column_count": len(rows[0]) if rows else 0,
            "size_bytes": len(file_content),
        }

        # If first row looks like headers, add them to metadata
        if rows and all(isinstance(cell, str) for cell in rows[0]):
            metadata["headers"] = rows[0]

        return ParsedDocument(content=text, metadata=metadata, file_type="text/csv")

    @property
    def supported_mime_types(self) -> list[str]:
        """
        List of MIME types supported by this parser.
        """
        return ["text/csv", "application/csv"]
