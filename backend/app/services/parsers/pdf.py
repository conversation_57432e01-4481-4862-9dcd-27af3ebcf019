"""
PDF document parser implementation.
"""

import io
from typing import Any

from pypdf import PdfReader

from app.services.parsers.base import BaseDocumentParser, ParsedDocument


class PDFParser(BaseDocumentParser):
    """
    Parser for PDF documents.
    """

    def parse(self, file_content: bytes) -> ParsedDocument:
        """
        Parse PDF content and extract text and metadata.

        Args:
            file_content: Raw PDF content as bytes

        Returns:
            ParsedDocument: Parsed document with content and metadata
        """
        pdf_file = io.BytesIO(file_content)
        reader = PdfReader(pdf_file)

        # Extract text from all pages
        text = ""
        for page in reader.pages:
            text += page.extract_text() or ""

        # Extract metadata
        metadata: dict[str, Any] = {}
        if reader.metadata:
            for key, value in reader.metadata.items():
                if value:
                    metadata[key] = value

        # Add page count to metadata
        metadata["page_count"] = len(reader.pages)

        return ParsedDocument(
            content=text, metadata=metadata, file_type="application/pdf"
        )

    @property
    def supported_mime_types(self) -> list[str]:
        """
        List of MIME types supported by this parser.
        """
        return ["application/pdf"]
