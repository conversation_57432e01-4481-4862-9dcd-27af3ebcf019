"""
Factory for creating document parsers based on file type.
"""

import os

from app.services.parsers.base import BaseDocumentParser
from app.services.parsers.csv import CSVParser
from app.services.parsers.docx import DocxParser
from app.services.parsers.pdf import PDFParser
from app.services.parsers.xlsx import XlsxParser


class DocumentParserFactory:
    """
    Factory for creating document parsers based on file type.
    """

    def __init__(self):
        self._parsers: dict[str, type[BaseDocumentParser]] = {}
        self._register_parsers()

    def _register_parsers(self):
        """Register all available parsers."""
        # Register parsers with their supported MIME types
        # Using a mapping to avoid creating instances just for MIME types
        parser_mappings = {
            PDFParser: ["application/pdf"],
            DocxParser: [
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword",
            ],
            XlsxParser: [
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-excel",
            ],
            CSVParser: ["text/csv", "application/csv"],
        }

        for parser_class, mime_types in parser_mappings.items():
            for mime_type in mime_types:
                self._parsers[mime_type] = parser_class

    def get_parser(
        self, file_path: str, content_type: str | None = None
    ) -> BaseDocumentParser | None:
        """
        Get a parser for the given file path and content type.

        Args:
            file_path: Path to the file
            content_type: MIME type of the file (optional)

        Returns:
            BaseDocumentParser: A parser instance or None if no parser is found
        """
        # If content_type is provided, use it directly
        if content_type and content_type in self._parsers:
            return self._parsers[content_type]()

        # Otherwise, try to determine from file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Map common extensions to MIME types
        extension_map = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".csv": "text/csv",
        }

        mime_type = extension_map.get(ext)
        if mime_type and mime_type in self._parsers:
            return self._parsers[mime_type]()

        return None

    def get_supported_types(self) -> list[str]:
        """
        Get a list of supported MIME types.

        Returns:
            list[str]: List of supported MIME types
        """
        return list(self._parsers.keys())
