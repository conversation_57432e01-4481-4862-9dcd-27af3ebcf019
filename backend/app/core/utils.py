"""
Utility functions for the YCA Collector application.
"""

import hashlib
import logging
import os
import re
import uuid
from datetime import datetime
from pathlib import Path

from fastapi import UploadFile

logger = logging.getLogger(__name__)


def generate_uuid() -> str:
    """Generate a random UUID as a string."""
    return str(uuid.uuid4())


def get_file_extension(filename: str) -> str:
    """Get the file extension from a filename.

    Args:
        filename: The name of the file

    Returns:
        The file extension in lowercase, or an empty string if no extension
    """
    if filename is None:
        raise ValueError("filename cannot be None")
    ext = os.path.splitext(filename)[1].lower()
    return ext.lstrip(".") if ext else ""


def get_file_size(file: UploadFile | str) -> int:
    """Get the size of a file in bytes.

    Args:
        file: Either a file path (str) or a FastAPI UploadFile object

    Returns:
        The file size in bytes
    """
    if isinstance(file, str):
        return os.path.getsize(file)

    # For UploadFile, we need to save the current position
    current_position = file.file.tell()
    file.file.seek(0, 2)  # Seek to the end of the file
    size = file.file.tell()
    file.file.seek(current_position)  # Go back to the original position
    return size


def get_file_hash(file: UploadFile | str, chunk_size: int = 8192) -> str:
    """Generate a SHA-256 hash of a file's contents.

    Args:
        file: Either a file path (str) or a FastAPI UploadFile object
        chunk_size: Number of bytes to read at a time

    Returns:
        The SHA-256 hash of the file contents
    """
    sha256_hash = hashlib.sha256()

    if isinstance(file, str):
        # Handle file path
        with open(file, "rb") as f:
            for byte_block in iter(lambda: f.read(chunk_size), b""):
                sha256_hash.update(byte_block)
    else:
        # Handle UploadFile
        file.file.seek(0)  # Ensure we're at the start of the file
        for chunk in iter(lambda: file.file.read(chunk_size), b""):
            sha256_hash.update(chunk)
        file.file.seek(0)  # Reset file pointer to the beginning

    return sha256_hash.hexdigest()


def create_directory_if_not_exists(directory: str) -> None:
    """Create a directory if it doesn't exist.

    Args:
        directory: Path to the directory to create
    """
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.error(f"Failed to create directory {directory}: {str(e)}")
        raise


def sanitize_filename(filename: str) -> str:
    """Sanitize a filename by removing or replacing invalid characters.

    Args:
        filename: The original filename

    Returns:
        A sanitized version of the filename
    """
    # Handle None or empty string
    if not filename:
        return "unnamed_file"
    # Replace spaces with underscores
    filename = filename.replace(" ", "_")

    # Handle path traversal by replacing slashes and dots with underscores
    filename = filename.replace("/", "_")
    filename = filename.replace("\\", "_")
    filename = re.sub(r"\.{2,}", "", filename)  # Remove multiple dots (path traversal)

    # Remove invalid characters including special symbols
    filename = re.sub(r'[*?:"<>|@#$%]', "", filename)

    # Clean up multiple underscores and leading/trailing underscores
    filename = re.sub(
        r"_{2,}", "_", filename
    )  # Replace multiple underscores with single
    filename = filename.strip("_")  # Remove leading/trailing underscores

    # Limit length
    max_length = 255  # Common filesystem limit
    if len(filename) > max_length:
        name, ext = os.path.splitext(filename)
        name = name[: max_length - len(ext)]
        filename = name + ext

    # Return default name if nothing left after sanitization
    return filename if filename else "unnamed_file"


def format_file_size(size_in_bytes: int, precision: int = 2) -> str:
    """Format a file size in a human-readable format.

    Args:
        size_in_bytes: The file size in bytes
        precision: Number of decimal places to include

    Returns:
        A formatted string with the appropriate unit (B, KB, MB, GB, TB)
    """
    for _unit in ["B", "KB", "MB", "GB", "TB"]:
        if size_in_bytes < 1024.0:
            break
        size_in_bytes /= 1024.0
    unit = _unit

    return f"{size_in_bytes:.{precision}f} {unit}"


def parse_date(date_str: str, formats: list[str] | None = None) -> datetime | None:
    """Parse a date string into a datetime object using multiple possible formats.

    Args:
        date_str: The date string to parse
        formats: List of date format strings to try (default: common date formats)

    Returns:
        A datetime object if parsing succeeds, None otherwise
    """
    if formats is None:
        formats = [
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d-%m-%Y",
            "%d/%m/%Y",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%d %H:%M:%S.%f",
            "%Y-%m-%dT%H:%M:%S.%fZ",
        ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    return None


def deep_merge_dicts(dict1: dict, dict2: dict) -> dict:
    """Recursively merge two dictionaries.

    Args:
        dict1: The first dictionary
        dict2: The second dictionary (takes precedence in case of conflicts)

    Returns:
        A new dictionary containing the merged contents of both input dictionaries
    """
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result
