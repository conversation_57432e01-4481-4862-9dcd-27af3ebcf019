"""Database configuration and session management."""

from collections.abc import Generator

from fastapi import HTT<PERSON>Exception, status
from supabase import Client, create_client

from app.core.config import get_settings

settings = get_settings()

# Initialize Supabase client
supabase: Client = create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)


def get_db() -> Generator[Client, None, None]:
    """Dependency for getting Supabase client."""
    try:
        yield supabase
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}",
        ) from e


# Import models to ensure they are registered with SQLAlchemy
# This must be after the database initialization to avoid circular imports
from app.models.database_models import *  # noqa: F401, F403, E402
