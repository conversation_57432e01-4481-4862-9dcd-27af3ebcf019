"""
API Router for YCA Collector - Phase 1

This module defines the main API router and includes Phase 1 endpoint routers.
"""

from fastapi import APIRouter

from app.api.endpoints import (
    advertisers,
    campaigns,
    creative_assets,
    health,
    insertion_orders,
    io_processing,
    line_items,
    personas,
    publishers,
)

# Create main API router
api_router = APIRouter(prefix="/api/v1")

# Include Phase 1 endpoint routers
api_router.include_router(
    health.router,
    prefix="",
    tags=["health"],
)

api_router.include_router(
    personas.router,
    prefix="",
    tags=["ai-personas"],
)

api_router.include_router(
    io_processing.router,
    prefix="/io",
    tags=["io-processing"],
)

api_router.include_router(publishers.router, prefix="/publishers", tags=["publishers"])
api_router.include_router(
    advertisers.router, prefix="/advertisers", tags=["advertisers"]
)
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(
    insertion_orders.router,
    prefix="/insertion-orders",
    tags=["insertion-orders"],
)
api_router.include_router(line_items.router, prefix="/line-items", tags=["line-items"])
api_router.include_router(
    creative_assets.router,
    prefix="/creative-assets",
    tags=["creative-assets"],
)
