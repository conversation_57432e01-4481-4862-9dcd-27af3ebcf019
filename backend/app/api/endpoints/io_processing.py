"""
API endpoints for IO processing - Phase 1 core functionality.
"""

import logging
from http import HTTPStatus
from typing import Any

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from app.api.deps import get_io_service
from app.services.io_service import IOService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/upload")
async def upload_io(
    file: UploadFile = File(...),
    advertiser_id: str = Form(...),
    io_name: str | None = Form(None),
    campaign_name: str | None = Form(None),
    notes: str | None = Form(None),
    io_service: IOService = Depends(get_io_service),
) -> dict[str, Any]:
    """
    Upload and process IO document (PDF, DOCX, XLSX)
    """
    try:
        result = await io_service.process_io_upload(
            file=file,
            advertiser_id=advertiser_id,
            campaign_name=campaign_name,
            # notes=notes # notes are not used in process_io_upload yet
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing IO upload: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error processing IO: {str(e)}",
        ) from e


@router.get("/status/{workflow_id}")
async def get_workflow_status(  # Renamed from get_processing_status
    workflow_id: str, io_service: IOService = Depends(get_io_service)
):
    """Get IO processing workflow status"""
    try:
        status = await io_service.get_workflow_status(workflow_id)
        return status
    except Exception as e:
        logger.error(f"Error getting workflow status: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error getting status: {str(e)}",
        ) from e


@router.get("/workflows")
async def list_workflows(
    limit: int = 100,  # Changed from 10 to 100 as per diff's apparent intent
    offset: int = 0,
    io_service: IOService = Depends(get_io_service),
):
    """List active IO processing workflows"""
    try:
        workflows = await io_service.list_workflows(limit, offset)
        return workflows
    except Exception as e:
        logger.error(f"Error listing workflows: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error listing workflows: {str(e)}",
        ) from e


@router.post("/workflows/{workflow_id}/retry")
async def retry_workflow(
    workflow_id: str, io_service: IOService = Depends(get_io_service)
):
    """Retry a failed IO processing workflow"""
    try:
        result = await io_service.retry_workflow(workflow_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error retrying workflow: {e}")
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error retrying workflow: {str(e)}",
        ) from e
