"""AI Personas endpoints."""

from typing import Any

from fastapi import APIRouter, HTTPException

from app.services.supabase_service import supabase_service

router = APIRouter()


@router.get("/personas", response_model=list[dict[str, Any]])
async def get_ai_personas() -> list[dict[str, Any]]:
    """Get all active AI personas from database."""
    try:
        personas = await supabase_service.get_ai_personas()

        if not personas:
            return []

        # Format personas for API response
        formatted_personas = []
        for persona in personas:
            formatted_personas.append(
                {
                    "id": persona["id"],
                    "name": persona["name"],
                    "description": persona["description"],
                    "tone": persona["tone"],
                    "style_guidelines": persona["style_guidelines"],
                    "template_data": persona["template_data"],
                    "is_active": persona["is_active"],
                    "created_at": persona["created_at"],
                }
            )

        return formatted_personas

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error fetching AI personas: {str(e)}"
        ) from e


@router.get("/personas/{persona_name}", response_model=dict[str, Any])
async def get_persona_by_name(persona_name: str) -> dict[str, Any]:
    """Get specific AI persona by name."""
    try:
        persona = await supabase_service.get_persona_by_name(persona_name)

        if not persona:
            raise HTTPException(
                status_code=404, detail=f"AI persona '{persona_name}' not found"
            )

        return {
            "id": persona["id"],
            "name": persona["name"],
            "description": persona["description"],
            "tone": persona["tone"],
            "style_guidelines": persona["style_guidelines"],
            "template_data": persona["template_data"],
            "is_active": persona["is_active"],
            "created_at": persona["created_at"],
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error fetching persona '{persona_name}': {str(e)}"
        ) from e


@router.get("/personas/random", response_model=dict[str, Any])
async def get_random_persona() -> dict[str, Any]:
    """Get a random AI persona for testing."""
    import random

    try:
        personas = await supabase_service.get_ai_personas()

        if not personas:
            raise HTTPException(
                status_code=404, detail="No AI personas found in database"
            )

        # Pick random persona
        random_persona = random.choice(personas)

        return {
            "id": random_persona["id"],
            "name": random_persona["name"],
            "description": random_persona["description"],
            "tone": random_persona["tone"],
            "template_data": random_persona["template_data"],
            "selected_for": "random_selection",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error getting random persona: {str(e)}"
        ) from e
