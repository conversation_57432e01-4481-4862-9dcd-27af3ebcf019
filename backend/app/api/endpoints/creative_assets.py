"""
API endpoints for managing creative assets.
"""

import contextlib
import os
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    HTTPException,
    UploadFile,
    status,
)
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.core.utils import generate_uuid, get_file_extension, sanitize_filename
from app.models.database_models import (
    Advertiser,
    Campaign,
    InsertionOrder,
    LineItem,
)
from app.models.database_models import (
    CreativeAsset as DBCreativeAsset,
)
from app.models.schemas import CreativeAsset, CreativeAssetCreate, CreativeAssetUpdate
from app.services.database_service import db
from app.services.file_service import file_service

router = APIRouter()

# Initialize the CRUD router with CreativeAsset model and schemas
creative_asset_router = CRUDRouter[
    CreativeAsset, CreativeAssetCreate, CreativeAssetUpdate
](
    model=CreativeAsset,
    create_schema=CreativeAssetCreate,
    update_schema=CreativeAssetUpdate,
    prefix="",
    tags=["creative-assets"],
)

# Include the CRUD routes
router.include_router(creative_asset_router.router)


# Additional custom endpoints
@router.post("/upload", response_model=CreativeAsset)
async def upload_creative_asset(
    file: UploadFile = File(...),
    line_item_id: UUID = Form(...),
    creative_type: str = Form(...),
    name: str | None = Form(None),
    description: str | None = Form(None),
    metadata: str | None = Form("{}"),
    db: Session = Depends(db.get_db),
):
    """
    Upload a new creative asset.

    Args:
        file: The file to upload
        line_item_id: ID of the line item this asset belongs to
        creative_type: Type of creative (e.g., 'banner', 'video', 'native')
        name: Optional name for the creative asset
        description: Optional description for the creative asset
        metadata: Optional JSON string containing additional metadata
        db: Database session

    Returns:
        The created creative asset
    """
    # Check if line item exists
    line_item = db.query(LineItem).filter(LineItem.id == line_item_id).first()
    if not line_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Line item with ID {line_item_id} not found",
        )

    # Generate a unique filename
    file_extension = get_file_extension(file.filename)
    original_filename = file.filename
    sanitized_filename = sanitize_filename(original_filename)

    try:
        # Create a unique directory for this line item's assets
        asset_dir = f"line_items/{line_item_id}"

        # Save the file and get its path and hash
        file_path, file_hash = await file_service.save_uploaded_file(
            file=file, destination_dir=asset_dir
        )

        # Get file size
        file_info = file_service.get_file_info(file_path)

        # Create the creative asset in the database
        db_asset = DBCreativeAsset(
            id=generate_uuid(),
            line_item_id=line_item_id,
            name=name or os.path.splitext(original_filename)[0],
            description=description,
            file_path=file_path,
            file_name=sanitized_filename,
            file_size=file_info["size"],
            file_hash=file_hash,
            file_type=file_extension.lstrip(".").upper(),
            creative_type=creative_type,
            status="pending_approval",
            metadata=metadata,
        )

        db.add(db_asset)
        db.commit()
        db.refresh(db_asset)

        return db_asset

    except Exception as e:
        db.rollback()
        # Clean up the file if it was saved but database operation failed
        if "file_path" in locals():
            with contextlib.suppress(Exception):
                file_service.delete_file(file_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload creative asset: {str(e)}",
        ) from e


@router.get("/{asset_id}/download")
async def download_creative_asset(asset_id: UUID, db: Session = Depends(db.get_db)):
    """
    Download a creative asset file.

    Args:
        asset_id: ID of the creative asset to download
        db: Database session

    Returns:
        The file for download
    """
    # Get the creative asset
    asset = db.query(DBCreativeAsset).filter(DBCreativeAsset.id == asset_id).first()
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Creative asset with ID {asset_id} not found",
        )

    # Get the full file path
    full_path = file_service.base_upload_dir / asset.file_path

    # Check if the file exists
    if not full_path.exists() or not full_path.is_file():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Creative asset file not found on server",
        )

    # Return the file for download
    return FileResponse(
        path=full_path,
        filename=asset.file_name,
        media_type="application/octet-stream",
        headers={"Content-Disposition": f"attachment; filename={asset.file_name}"},
    )


@router.get("/by-line-item/{line_item_id}", response_model=list[CreativeAsset])
async def get_creative_assets_by_line_item(
    line_item_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific line item.

    Args:
        line_item_id: ID of the line item
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified line item
    """
    # Check if line item exists
    line_item = db.query(LineItem).filter(LineItem.id == line_item_id).first()
    if not line_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Line item with ID {line_item_id} not found",
        )

    # Build query
    query = db.query(DBCreativeAsset).filter(
        DBCreativeAsset.line_item_id == line_item_id
    )

    # Apply filters
    if status:
        query = query.filter(DBCreativeAsset.status == status)
    if creative_type:
        query = query.filter(DBCreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.get(
    "/by-insertion-order/{insertion_order_id}", response_model=list[CreativeAsset]
)
async def get_creative_assets_by_insertion_order(
    insertion_order_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific insertion order.

    Args:
        insertion_order_id: ID of the insertion order
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified insertion order
    """
    # Check if insertion order exists
    insertion_order = (
        db.query(InsertionOrder).filter(InsertionOrder.id == insertion_order_id).first()
    )
    if not insertion_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Insertion order with ID {insertion_order_id} not found",
        )

    # Build query
    query = (
        db.query(DBCreativeAsset)
        .join(LineItem)
        .filter(LineItem.insertion_order_id == insertion_order_id)
    )

    # Apply filters
    if status:
        query = query.filter(DBCreativeAsset.status == status)
    if creative_type:
        query = query.filter(DBCreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.get("/by-campaign/{campaign_id}", response_model=list[CreativeAsset])
async def get_creative_assets_by_campaign(
    campaign_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific campaign.

    Args:
        campaign_id: ID of the campaign
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified campaign
    """
    # Check if campaign exists
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found",
        )

    # Build query
    query = (
        db.query(DBCreativeAsset)
        .join(LineItem)
        .join(InsertionOrder)
        .filter(InsertionOrder.campaign_id == campaign_id)
    )

    # Apply filters
    if status:
        query = query.filter(DBCreativeAsset.status == status)
    if creative_type:
        query = query.filter(DBCreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.get("/by-advertiser/{advertiser_id}", response_model=list[CreativeAsset])
async def get_creative_assets_by_advertiser(
    advertiser_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific advertiser.

    Args:
        advertiser_id: ID of the advertiser
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified advertiser
    """
    # Check if advertiser exists
    advertiser = db.query(Advertiser).filter(Advertiser.id == advertiser_id).first()
    if not advertiser:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Advertiser with ID {advertiser_id} not found",
        )

    # Build query
    query = (
        db.query(DBCreativeAsset)
        .join(LineItem)
        .join(InsertionOrder)
        .join(Campaign)
        .filter(Campaign.advertiser_id == advertiser_id)
    )

    # Apply filters
    if status:
        query = query.filter(DBCreativeAsset.status == status)
    if creative_type:
        query = query.filter(DBCreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_creative_asset(asset_id: UUID, db: Session = Depends(db.get_db)):
    """
    Delete a creative asset.

    Args:
        asset_id: ID of the creative asset to delete
        db: Database session

    Returns:
        No content on success
    """
    # Get the creative asset
    asset = db.query(DBCreativeAsset).filter(DBCreativeAsset.id == asset_id).first()
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Creative asset with ID {asset_id} not found",
        )

    try:
        # Delete the file from storage
        file_service.delete_file(asset.file_path)

        # Delete the asset from the database
        db.delete(asset)
        db.commit()

        return None

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete creative asset: {str(e)}",
        ) from e
