"""
Base endpoint module with common functionality for all API endpoints.
"""

from typing import Generic, TypeVar
from uuid import UUID

from fastapi import APIRouter, status
from pydantic import BaseModel

# Define generic types
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDRouter(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    Generic CRUD router with default endpoints for Create, Read, Update, Delete operations.

    To use, subclass this and implement the required methods.
    """

    def __init__(
        self,
        model: type[ModelType],
        create_schema: type[CreateSchemaType],
        update_schema: type[UpdateSchemaType],
        prefix: str = "",
        tags: list[str] | None = None,
    ):
        """
        Initialize the CRUD router.

        Args:
            model: The SQLAlchemy model class
            create_schema: Pydantic model for creating items
            update_schema: Pydantic model for updating items
            prefix: URL prefix for all routes
            tags: OpenAPI tags
        """
        self.model = model
        self.create_schema = create_schema
        self.update_schema = update_schema
        self.prefix = prefix
        self.tags = tags or []

        # Create router
        self.router = APIRouter()
        self._setup_routes()

    def _setup_routes(self) -> None:
        """Set up the CRUD routes."""
        self.router.add_api_route(
            "/",
            self.list,
            methods=["GET"],
            response_model=list[self.model],
            summary=f"List {self.model.__name__} items",
        )

        self.router.add_api_route(
            "/{item_id}",
            self.get,
            methods=["GET"],
            response_model=self.model,
            summary=f"Get a {self.model.__name__} by ID",
        )

        self.router.add_api_route(
            "/",
            self.create,
            methods=["POST"],
            response_model=self.model,
            status_code=status.HTTP_201_CREATED,
            summary=f"Create a new {self.model.__name__}",
        )

        self.router.add_api_route(
            "/{item_id}",
            self.update,
            methods=["PUT"],
            response_model=self.model,
            summary=f"Update a {self.model.__name__}",
        )

        self.router.add_api_route(
            "/{item_id}",
            self.delete,
            methods=["DELETE"],
            status_code=status.HTTP_204_NO_CONTENT,
            summary=f"Delete a {self.model.__name__}",
        )

    async def list(
        self,
        skip: int = 0,
        limit: int = 100,
    ) -> list[ModelType]:
        """List items with pagination."""
        raise NotImplementedError("List method not implemented")

    async def get(self, item_id: UUID) -> ModelType:
        """Get a single item by ID."""
        raise NotImplementedError("Get method not implemented")

    async def create(
        self,
        item_in: CreateSchemaType,
    ) -> ModelType:
        """Create a new item."""
        raise NotImplementedError("Create method not implemented")

    async def update(
        self,
        item_id: UUID,
        item_in: UpdateSchemaType,
    ) -> ModelType:
        """Update an existing item."""
        raise NotImplementedError("Update method not implemented")

    async def delete(self, item_id: UUID) -> None:
        """Delete an item by ID."""
        raise NotImplementedError("Delete method not implemented")
