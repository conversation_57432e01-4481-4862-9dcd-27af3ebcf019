"""
API endpoints for managing advertisers.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.models.database_models import (
    Advertiser as AdvertiserModel,
)
from app.models.database_models import (
    Campaign,
    Publisher,
)
from app.models.schemas import Advertiser, AdvertiserCreate, AdvertiserUpdate
from app.services.database_service import db

router = APIRouter()

# Initialize the CRUD router with Advertiser model and schemas
advertiser_router = CRUDRouter[Advertiser, AdvertiserCreate, AdvertiserUpdate](
    model=Advertiser,
    create_schema=AdvertiserCreate,
    update_schema=AdvertiserUpdate,
    prefix="",
    tags=["advertisers"],
)

# Include the CRUD routes
router.include_router(advertiser_router.router)


# Additional custom endpoints
@router.get("/{advertiser_id}/campaigns", response_model=list[dict])
async def get_advertiser_campaigns(
    advertiser_id: UUID,
    skip: int = 0,
    limit: int = 100,
    status: str | None = None,
    db: Session = Depends(db.get_db),
):
    """
    Get all campaigns for a specific advertiser.

    Args:
        advertiser_id: ID of the advertiser
        skip: Number of records to skip
        limit: Maximum number of records to return
        status: Optional status to filter campaigns by
        db: Database session

    Returns:
        List of campaigns for the specified advertiser
    """
    # Check if advertiser exists
    advertiser = (
        db.query(AdvertiserModel).filter(AdvertiserModel.id == advertiser_id).first()
    )
    if not advertiser:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Advertiser with ID {advertiser_id} not found",
        )

    # Build query
    query = db.query(Campaign).filter(Campaign.advertiser_id == advertiser_id)

    # Apply status filter if provided
    if status:
        query = query.filter(Campaign.status == status)

    # Apply pagination
    campaigns = query.offset(skip).limit(limit).all()

    return campaigns


@router.get("/{advertiser_id}/publisher", response_model=dict)
async def get_advertiser_publisher(
    advertiser_id: UUID, db: Session = Depends(db.get_db)
):
    """
    Get the publisher for a specific advertiser.

    Args:
        advertiser_id: ID of the advertiser
        db: Database session

    Returns:
        The publisher for the specified advertiser
    """
    # Check if advertiser exists and get publisher
    advertiser = (
        db.query(AdvertiserModel)
        .join(Publisher)
        .filter(AdvertiserModel.id == advertiser_id)
        .first()
    )

    if not advertiser:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Advertiser with ID {advertiser_id} not found",
        )

    return advertiser.publisher


@router.get("/by-publisher/{publisher_id}", response_model=list[dict])
async def get_advertisers_by_publisher(
    publisher_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all advertisers for a specific publisher.

    Args:
        publisher_id: ID of the publisher
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of advertisers for the specified publisher
    """
    # Check if publisher exists
    publisher = db.query(Publisher).filter(Publisher.id == publisher_id).first()
    if not publisher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Publisher with ID {publisher_id} not found",
        )

    # Get advertisers for the publisher
    advertisers = (
        db.query(AdvertiserModel)
        .filter(AdvertiserModel.publisher_id == publisher_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

    return advertisers
