"""
API endpoints for managing insertion orders.
"""

from datetime import date, datetime
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.models.database_models import (
    Campaign,
    CreativeAsset,
    LineItem,
)
from app.models.database_models import (
    InsertionOrder as InsertionOrderModel,
)
from app.models.schemas import (
    InsertionOrder,
    InsertionOrderCreate,
    InsertionOrderUpdate,
)
from app.services.database_service import db

router = APIRouter()

# Initialize the CRUD router with InsertionOrder model and schemas
insertion_order_router = CRUDRouter[
    InsertionOrder, InsertionOrderCreate, InsertionOrderUpdate
](
    model=InsertionOrder,
    create_schema=InsertionOrderCreate,
    update_schema=InsertionOrderUpdate,
    prefix="",
    tags=["insertion-orders"],
)

# Include the CRUD routes
router.include_router(insertion_order_router.router)


# Additional custom endpoints
@router.get("/{insertion_order_id}/line-items", response_model=list[dict])
async def get_insertion_order_line_items(
    insertion_order_id: UUID,
    status: str | None = None,
    format: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items for a specific insertion order.

    Args:
        insertion_order_id: ID of the insertion order
        status: Optional status to filter line items by
        format: Optional format to filter line items by (e.g., '728x90', '300x250')
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items for the specified insertion order
    """
    # Check if insertion order exists
    insertion_order = (
        db.query(InsertionOrderModel)
        .filter(InsertionOrderModel.id == insertion_order_id)
        .first()
    )
    if not insertion_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Insertion order with ID {insertion_order_id} not found",
        )

    # Build query
    query = db.query(LineItem).filter(LineItem.insertion_order_id == insertion_order_id)

    # Apply filters
    if status:
        query = query.filter(LineItem.status == status)
    if format:
        query = query.filter(LineItem.format == format)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items


@router.get("/{insertion_order_id}/creative-assets", response_model=list[dict])
async def get_insertion_order_creative_assets(
    insertion_order_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific insertion order.

    Args:
        insertion_order_id: ID of the insertion order
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified insertion order
    """
    # Check if insertion order exists
    insertion_order = (
        db.query(InsertionOrderModel)
        .filter(InsertionOrderModel.id == insertion_order_id)
        .first()
    )
    if not insertion_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Insertion order with ID {insertion_order_id} not found",
        )

    # Build query
    query = (
        db.query(CreativeAsset)
        .join(LineItem)
        .filter(LineItem.insertion_order_id == insertion_order_id)
    )

    # Apply filters
    if status:
        query = query.filter(CreativeAsset.status == status)
    if creative_type:
        query = query.filter(CreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.get("/by-campaign/{campaign_id}", response_model=list[dict])
async def get_insertion_orders_by_campaign(
    campaign_id: UUID,
    status: str | None = None,
    start_date: date | None = None,
    end_date: date | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all insertion orders for a specific campaign.

    Args:
        campaign_id: ID of the campaign
        status: Optional status to filter insertion orders by
        start_date: Optional start date to filter insertion orders by
        end_date: Optional end date to filter insertion orders by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of insertion orders for the specified campaign
    """
    # Check if campaign exists
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found",
        )

    # Build query
    query = db.query(InsertionOrderModel).filter(
        InsertionOrderModel.campaign_id == campaign_id
    )

    # Apply filters
    if status:
        query = query.filter(InsertionOrderModel.processing_status == status)

    if start_date:
        query = query.filter(
            or_(
                InsertionOrderModel.end_date.is_(None),
                InsertionOrderModel.end_date >= start_date,
            )
        )

    if end_date:
        query = query.filter(
            or_(
                InsertionOrderModel.start_date.is_(None),
                InsertionOrderModel.start_date <= end_date,
            )
        )

    # Apply pagination
    insertion_orders = (
        query.order_by(InsertionOrderModel.start_date.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

    return insertion_orders


@router.get("/{insertion_order_id}/status")
async def get_insertion_order_status(
    insertion_order_id: UUID, db: Session = Depends(db.get_db)
):
    """
    Get the processing status of an insertion order and its line items.

    Args:
        insertion_order_id: ID of the insertion order
        db: Database session

    Returns:
        Dictionary with status information for the insertion order and its line items
    """
    # Check if insertion order exists
    insertion_order = (
        db.query(InsertionOrderModel)
        .filter(InsertionOrderModel.id == insertion_order_id)
        .first()
    )
    if not insertion_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Insertion order with ID {insertion_order_id} not found",
        )

    # Get line items for the insertion order
    line_items = (
        db.query(LineItem)
        .filter(LineItem.insertion_order_id == insertion_order_id)
        .all()
    )

    # Count line items by status
    status_counts = {}
    for li in line_items:
        status_counts[li.status] = status_counts.get(li.status, 0) + 1

    return {
        "insertion_order_id": str(insertion_order_id),
        "processing_status": insertion_order.processing_status,
        "total_line_items": len(line_items),
        "line_items_by_status": status_counts,
        "last_updated": datetime.utcnow().isoformat(),
    }
