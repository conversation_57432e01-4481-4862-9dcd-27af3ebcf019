"""
API endpoints for managing line items.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.models.database_models import (
    Advertiser,
    Campaign,
    CreativeAsset,
    InsertionOrder,
)
from app.models.database_models import (
    LineItem as LineItemModel,
)
from app.models.schemas import LineItem, LineItemCreate, LineItemUpdate
from app.services.database_service import db

router = APIRouter()

# Initialize the CRUD router with LineItem model and schemas
line_item_router = CRUDRouter[LineItem, LineItemCreate, LineItemUpdate](
    model=LineItem,
    create_schema=LineItemCreate,
    update_schema=LineItemUpdate,
    prefix="",
    tags=["line-items"],
)

# Include the CRUD routes
router.include_router(line_item_router.router)


# Additional custom endpoints
@router.get("/{line_item_id}/creative-assets", response_model=list[dict])
async def get_line_item_creative_assets(
    line_item_id: UUID,
    status: str | None = None,
    creative_type: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all creative assets for a specific line item.

    Args:
        line_item_id: ID of the line item
        status: Optional status to filter creative assets by
        creative_type: Optional type to filter creative assets by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of creative assets for the specified line item
    """
    # Check if line item exists
    line_item = db.query(LineItemModel).filter(LineItemModel.id == line_item_id).first()
    if not line_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Line item with ID {line_item_id} not found",
        )

    # Build query
    query = db.query(CreativeAsset).filter(CreativeAsset.line_item_id == line_item_id)

    # Apply filters
    if status:
        query = query.filter(CreativeAsset.status == status)
    if creative_type:
        query = query.filter(CreativeAsset.creative_type == creative_type)

    # Apply pagination
    creative_assets = query.offset(skip).limit(limit).all()

    return creative_assets


@router.get("/by-insertion-order/{insertion_order_id}", response_model=list[dict])
async def get_line_items_by_insertion_order(
    insertion_order_id: UUID,
    status: str | None = None,
    format: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items for a specific insertion order.

    Args:
        insertion_order_id: ID of the insertion order
        status: Optional status to filter line items by
        format: Optional format to filter line items by (e.g., '728x90', '300x250')
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items for the specified insertion order
    """
    # Check if insertion order exists
    insertion_order = (
        db.query(InsertionOrder).filter(InsertionOrder.id == insertion_order_id).first()
    )
    if not insertion_order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Insertion order with ID {insertion_order_id} not found",
        )

    # Build query
    query = db.query(LineItemModel).filter(
        LineItemModel.insertion_order_id == insertion_order_id
    )

    # Apply filters
    if status:
        query = query.filter(LineItemModel.status == status)
    if format:
        query = query.filter(LineItemModel.format == format)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items


@router.get("/by-campaign/{campaign_id}", response_model=list[dict])
async def get_line_items_by_campaign(
    campaign_id: UUID,
    status: str | None = None,
    format: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items for a specific campaign.

    Args:
        campaign_id: ID of the campaign
        status: Optional status to filter line items by
        format: Optional format to filter line items by (e.g., '728x90', '300x250')
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items for the specified campaign
    """
    # Check if campaign exists
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found",
        )

    # Build query
    query = (
        db.query(LineItemModel)
        .join(InsertionOrder)
        .filter(InsertionOrder.campaign_id == campaign_id)
    )

    # Apply filters
    if status:
        query = query.filter(LineItemModel.status == status)
    if format:
        query = query.filter(LineItemModel.format == format)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items


@router.get("/by-advertiser/{advertiser_id}", response_model=list[dict])
async def get_line_items_by_advertiser(
    advertiser_id: UUID,
    status: str | None = None,
    format: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items for a specific advertiser.

    Args:
        advertiser_id: ID of the advertiser
        status: Optional status to filter line items by
        format: Optional format to filter line items by (e.g., '728x90', '300x250')
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items for the specified advertiser
    """
    # Check if advertiser exists
    advertiser = db.query(Advertiser).filter(Advertiser.id == advertiser_id).first()
    if not advertiser:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Advertiser with ID {advertiser_id} not found",
        )

    # Build query
    query = (
        db.query(LineItemModel)
        .join(InsertionOrder)
        .join(Campaign)
        .filter(Campaign.advertiser_id == advertiser_id)
    )

    # Apply filters
    if status:
        query = query.filter(LineItemModel.status == status)
    if format:
        query = query.filter(LineItemModel.format == format)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items


@router.get("/by-format/{format}", response_model=list[dict])
async def get_line_items_by_format(
    format: str,
    status: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items with a specific format.

    Args:
        format: The format to filter by (e.g., '728x90', '300x250')
        status: Optional status to filter line items by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items with the specified format
    """
    # Build query
    query = db.query(LineItemModel).filter(LineItemModel.format == format)

    # Apply status filter if provided
    if status:
        query = query.filter(LineItemModel.status == status)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items
