"""Health check endpoints."""

from datetime import datetime
from typing import Any

from fastapi import APIRouter, HTTPException

from app.core.config import get_settings
from app.services.supabase_service import supabase_service

router = APIRouter()
settings = get_settings()


@router.get("/health", response_model=dict[str, Any])
async def health_check() -> dict[str, Any]:
    """Comprehensive health check endpoint."""
    # Test database connection
    db_status = "ok" if await supabase_service.test_connection() else "error"

    # Test AI personas availability
    personas = await supabase_service.get_ai_personas()
    ai_status = "ok" if len(personas) > 0 else "error"

    return {
        "status": "ok" if db_status == "ok" and ai_status == "ok" else "degraded",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": settings.APP_ENV,
        "debug": settings.APP_DEBUG,
        "services": {
            "database": db_status,
            "ai_personas": ai_status,
            "personas_count": len(personas),
            "gemini_api": "configured"
            if settings.GEMINI_API_KEY != "your-gemini-api-key-here"
            else "not_configured",
        },
    }


@router.get("/health/db", response_model=dict[str, Any])
async def health_check_db() -> dict[str, Any]:
    """Database-specific health check endpoint."""
    try:
        connection_ok = await supabase_service.test_connection()

        if not connection_ok:
            raise HTTPException(
                status_code=500, detail={"database": "connection_failed"}
            )

        # Get counts of main tables
        personas = await supabase_service.get_ai_personas()
        publishers = await supabase_service.get_publishers()

        return {
            "database": "ok",
            "connection": "established",
            "tables": {"ai_personas": len(personas), "publishers": len(publishers)},
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail={"database": f"error: {str(e)}"}
        ) from e
