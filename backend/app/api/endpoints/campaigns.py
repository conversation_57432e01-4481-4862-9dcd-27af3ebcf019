"""
API endpoints for managing campaigns.
"""

from datetime import date
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.models.database_models import (
    Advertiser,
    InsertionOrder,
    LineItem,
)
from app.models.database_models import (
    Campaign as CampaignModel,
)
from app.models.schemas import Campaign, CampaignCreate, CampaignUpdate
from app.services.database_service import db

router = APIRouter()

# Initialize the CRUD router with Campaign model and schemas
campaign_router = CRUDRouter[Campaign, CampaignCreate, CampaignUpdate](
    model=Campaign,
    create_schema=CampaignCreate,
    update_schema=CampaignUpdate,
    prefix="",
    tags=["campaigns"],
)

# Include the CRUD routes
router.include_router(campaign_router.router)


# Additional custom endpoints
@router.get("/{campaign_id}/insertion-orders", response_model=list[dict])
async def get_campaign_insertion_orders(
    campaign_id: UUID,
    status: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all insertion orders for a specific campaign.

    Args:
        campaign_id: ID of the campaign
        status: Optional status to filter insertion orders by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of insertion orders for the specified campaign
    """
    # Check if campaign exists
    campaign = db.query(CampaignModel).filter(CampaignModel.id == campaign_id).first()
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found",
        )

    # Build query
    query = db.query(InsertionOrder).filter(InsertionOrder.campaign_id == campaign_id)

    # Apply status filter if provided
    if status:
        query = query.filter(InsertionOrder.processing_status == status)

    # Apply pagination
    insertion_orders = query.offset(skip).limit(limit).all()

    return insertion_orders


@router.get("/{campaign_id}/line-items", response_model=list[dict])
async def get_campaign_line_items(
    campaign_id: UUID,
    status: str | None = None,
    format: str | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all line items for a specific campaign.

    Args:
        campaign_id: ID of the campaign
        status: Optional status to filter line items by
        format: Optional format to filter line items by (e.g., '728x90', '300x250')
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of line items for the specified campaign
    """
    # Check if campaign exists
    campaign = db.query(CampaignModel).filter(CampaignModel.id == campaign_id).first()
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found",
        )

    # Build query
    query = (
        db.query(LineItem)
        .join(InsertionOrder)
        .filter(InsertionOrder.campaign_id == campaign_id)
    )

    # Apply filters
    if status:
        query = query.filter(LineItem.status == status)
    if format:
        query = query.filter(LineItem.format == format)

    # Apply pagination
    line_items = query.offset(skip).limit(limit).all()

    return line_items


@router.get("/active/count")
async def count_active_campaigns(db: Session = Depends(db.get_db)):
    """
    Count all active campaigns.

    Args:
        db: Database session

    Returns:
        Dictionary with the count of active campaigns
    """
    today = date.today()
    count = (
        db.query(CampaignModel)
        .filter(
            and_(
                CampaignModel.status == "active",
                or_(CampaignModel.end_date.is_(None), CampaignModel.end_date >= today),
            )
        )
        .count()
    )

    return {"count": count}


@router.get("/by-advertiser/{advertiser_id}", response_model=list[dict])
async def get_campaigns_by_advertiser(
    advertiser_id: UUID,
    status: str | None = None,
    start_date: date | None = None,
    end_date: date | None = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all campaigns for a specific advertiser with optional filters.

    Args:
        advertiser_id: ID of the advertiser
        status: Optional status to filter campaigns by
        start_date: Optional start date to filter campaigns by
        end_date: Optional end date to filter campaigns by
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of campaigns for the specified advertiser
    """
    # Check if advertiser exists
    advertiser = db.query(Advertiser).filter(Advertiser.id == advertiser_id).first()
    if not advertiser:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Advertiser with ID {advertiser_id} not found",
        )

    # Build query
    query = db.query(CampaignModel).filter(CampaignModel.advertiser_id == advertiser_id)

    # Apply filters
    if status:
        query = query.filter(CampaignModel.status == status)

    if start_date:
        query = query.filter(
            or_(CampaignModel.end_date.is_(None), CampaignModel.end_date >= start_date)
        )

    if end_date:
        query = query.filter(
            or_(
                CampaignModel.start_date.is_(None), CampaignModel.start_date <= end_date
            )
        )

    # Apply pagination
    campaigns = (
        query.order_by(CampaignModel.start_date.desc()).offset(skip).limit(limit).all()
    )

    return campaigns
