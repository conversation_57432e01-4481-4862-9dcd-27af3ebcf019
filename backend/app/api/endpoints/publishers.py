"""
API endpoints for managing publishers.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.endpoints.base import CRUDRouter
from app.models.database_models import Advertiser, Campaign
from app.models.schemas import Publisher, PublisherCreate, PublisherUpdate
from app.services.database_service import db

router = APIRouter()

# Initialize the CRUD router with Publisher model and schemas
publisher_router = CRUDRouter[Publisher, PublisherCreate, PublisherUpdate](
    model=Publisher,
    create_schema=PublisherCreate,
    update_schema=PublisherUpdate,
    prefix="",
    tags=["publishers"],
)

# Include the CRUD routes
router.include_router(publisher_router.router)


# Additional custom endpoints can be added here
@router.get("/{publisher_id}/advertisers", response_model=list[dict])
async def get_publisher_advertisers(
    publisher_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all advertisers for a specific publisher.

    Args:
        publisher_id: ID of the publisher
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of advertisers for the specified publisher
    """
    # Check if publisher exists
    publisher = db.query(Publisher).filter(Publisher.id == publisher_id).first()
    if not publisher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Publisher with ID {publisher_id} not found",
        )

    # Get advertisers for the publisher
    advertisers = (
        db.query(Advertiser)
        .filter(Advertiser.publisher_id == publisher_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

    return advertisers


@router.get("/{publisher_id}/campaigns", response_model=list[dict])
async def get_publisher_campaigns(
    publisher_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(db.get_db),
):
    """
    Get all campaigns for a specific publisher.

    Args:
        publisher_id: ID of the publisher
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session

    Returns:
        List of campaigns for the specified publisher
    """
    # Check if publisher exists
    publisher = db.query(Publisher).filter(Publisher.id == publisher_id).first()
    if not publisher:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Publisher with ID {publisher_id} not found",
        )

    # Get campaigns for the publisher through advertisers
    campaigns = (
        db.query(Campaign)
        .join(Advertiser)
        .filter(Advertiser.publisher_id == publisher_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

    return campaigns
