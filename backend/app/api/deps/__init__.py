"""Dependencies for API endpoints."""

from fastapi import Depends

from app.services.database_service import DatabaseService, get_db_service
from app.services.file_service import FileService, get_file_service
from app.services.io_service import IOService


def get_io_service(
    db_service: DatabaseService = Depends(get_db_service),
    file_service: FileService = Depends(get_file_service),
) -> IOService:
    """
    Dependency that provides an instance of the IOService.
    """
    return IOService(db_service=db_service, file_service=file_service)


# This file makes the deps directory a Python package
