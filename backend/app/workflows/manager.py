"""
Workflow Manager for LangGraph workflows.

This module provides a centralized manager for registering, executing,
and monitoring all LangGraph workflows in the YCA Collector system.
"""

import asyncio
import logging
from datetime import datetime
from enum import Enum
from typing import TYPE_CHECKING, Any

from app.workflows.io_processing.workflow import IOProcessingWorkflow

if TYPE_CHECKING:
    from app.workflows.base import BaseWorkflow

logger = logging.getLogger(__name__)


class WorkflowType(Enum):
    """Enumeration of available workflow types."""

    IO_PROCESSING = "io_processing"
    COMMUNICATION = "communication"
    ASSET_VALIDATION = "asset_validation"


class WorkflowStatus(Enum):
    """Enumeration of workflow execution statuses."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowExecution:
    """Represents a single workflow execution."""

    def __init__(
        self, workflow_id: str, workflow_type: WorkflowType, input_data: dict[str, Any]
    ):
        self.workflow_id = workflow_id
        self.workflow_type = workflow_type
        self.input_data = input_data
        self.status = WorkflowStatus.PENDING
        self.created_at = datetime.utcnow()
        self.started_at: datetime | None = None
        self.completed_at: datetime | None = None
        self.result: dict[str, Any] | None = None
        self.error: str | None = None


class WorkflowManager:
    """Central manager for all LangGraph workflows."""

    def __init__(self):
        self._workflows: dict[WorkflowType, type[BaseWorkflow]] = {}
        self._executions: dict[str, WorkflowExecution] = {}
        self._running_tasks: dict[str, asyncio.Task] = {}

        # Register available workflows
        self._register_workflows()

        logger.info("WorkflowManager initialized")

    def _register_workflows(self):
        """Register all available workflow types."""
        self._workflows[WorkflowType.IO_PROCESSING] = IOProcessingWorkflow

        logger.info(f"Registered {len(self._workflows)} workflow types")

    async def execute_workflow(
        self,
        workflow_type: WorkflowType,
        input_data: dict[str, Any],
        workflow_id: str | None = None,
    ) -> str:
        """
        Execute a workflow asynchronously.

        Args:
            workflow_type: Type of workflow to execute
            input_data: Input data for the workflow
            workflow_id: Optional custom workflow ID

        Returns:
            Workflow execution ID
        """
        if workflow_type not in self._workflows:
            raise ValueError(f"Unknown workflow type: {workflow_type}")

        # Create workflow execution record
        execution = WorkflowExecution(
            workflow_id=workflow_id or self._generate_workflow_id(),
            workflow_type=workflow_type,
            input_data=input_data,
        )

        self._executions[execution.workflow_id] = execution

        # Start workflow execution as background task
        task = asyncio.create_task(
            self._run_workflow(execution), name=f"workflow_{execution.workflow_id}"
        )

        self._running_tasks[execution.workflow_id] = task

        logger.info(
            f"Started workflow {execution.workflow_id} of type {workflow_type.value}"
        )

        return execution.workflow_id

    async def _run_workflow(self, execution: WorkflowExecution):
        """Run a workflow execution."""
        try:
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.utcnow()

            # Create workflow instance
            workflow_class = self._workflows[execution.workflow_type]
            workflow = workflow_class()

            # Execute workflow
            result = await workflow.execute(execution.input_data)

            # Update execution record
            execution.result = result
            execution.status = (
                WorkflowStatus.COMPLETED
                if result.get("success")
                else WorkflowStatus.FAILED
            )
            execution.completed_at = datetime.utcnow()

            if not result.get("success"):
                execution.error = result.get("error", "Unknown error")

            logger.info(
                f"Workflow {execution.workflow_id} completed with status {execution.status.value}"
            )

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.error = str(e)
            execution.completed_at = datetime.utcnow()

            logger.error(f"Workflow {execution.workflow_id} failed: {str(e)}")

        finally:
            # Clean up running task reference
            if execution.workflow_id in self._running_tasks:
                del self._running_tasks[execution.workflow_id]

    def get_workflow_status(self, workflow_id: str) -> dict[str, Any] | None:
        """Get the status of a workflow execution."""
        execution = self._executions.get(workflow_id)
        if not execution:
            return None

        return {
            "workflow_id": execution.workflow_id,
            "workflow_type": execution.workflow_type.value,
            "status": execution.status.value,
            "created_at": execution.created_at.isoformat(),
            "started_at": execution.started_at.isoformat()
            if execution.started_at
            else None,
            "completed_at": execution.completed_at.isoformat()
            if execution.completed_at
            else None,
            "result": execution.result,
            "error": execution.error,
        }

    def list_workflows(
        self, status: WorkflowStatus | None = None
    ) -> list[dict[str, Any]]:
        """List all workflow executions, optionally filtered by status."""
        executions = self._executions.values()

        if status:
            executions = [e for e in executions if e.status == status]

        return [
            {
                "workflow_id": e.workflow_id,
                "workflow_type": e.workflow_type.value,
                "status": e.status.value,
                "created_at": e.created_at.isoformat(),
                "started_at": e.started_at.isoformat() if e.started_at else None,
                "completed_at": e.completed_at.isoformat() if e.completed_at else None,
                "has_error": e.error is not None,
            }
            for e in sorted(executions, key=lambda x: x.created_at, reverse=True)
        ]

    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a running workflow."""
        execution = self._executions.get(workflow_id)
        if not execution:
            return False

        task = self._running_tasks.get(workflow_id)
        if task and not task.done():
            task.cancel()
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = datetime.utcnow()

            logger.info(f"Cancelled workflow {workflow_id}")
            return True

        return False

    async def retry_workflow(self, workflow_id: str) -> str | None:
        """Retry a failed workflow with the same input data."""
        execution = self._executions.get(workflow_id)
        if not execution or execution.status != WorkflowStatus.FAILED:
            return None

        # Create new execution with same input data
        new_workflow_id = await self.execute_workflow(
            execution.workflow_type, execution.input_data
        )

        logger.info(f"Retrying workflow {workflow_id} as {new_workflow_id}")

        return new_workflow_id

    def _generate_workflow_id(self) -> str:
        """Generate a unique workflow ID."""
        from app.core.utils import generate_uuid

        return generate_uuid()

    def get_available_workflow_types(self) -> list[str]:
        """Get list of available workflow types."""
        return [wf_type.value for wf_type in self._workflows]

    def get_execution_metrics(self) -> dict[str, Any]:
        """Get workflow execution metrics."""
        total_executions = len(self._executions)
        running_count = len(self._running_tasks)

        status_counts = {}
        for status in WorkflowStatus:
            count = sum(1 for e in self._executions.values() if e.status == status)
            status_counts[status.value] = count

        return {
            "total_executions": total_executions,
            "running_executions": running_count,
            "status_breakdown": status_counts,
            "available_workflow_types": self.get_available_workflow_types(),
        }


# Global workflow manager instance
workflow_manager = WorkflowManager()


def get_workflow_manager() -> WorkflowManager:
    """Get the global workflow manager instance."""
    return workflow_manager
