"""
Base workflow components for LangGraph integration.

This module provides the foundational classes and state management
for all YCA Collector workflows.
"""

import uuid
from datetime import datetime
from typing import Any, TypedDict

from langgraph.graph import StateGraph
from langgraph.graph.message import AnyMessage
from pydantic import BaseModel


class WorkflowState(TypedDict):
    """Base state for all YCA Collector workflows."""

    # Workflow metadata
    workflow_id: str
    workflow_type: str
    status: str  # "pending", "in_progress", "completed", "failed"
    created_at: datetime
    updated_at: datetime

    # Data payload
    input_data: dict[str, Any]
    output_data: dict[str, Any]

    # Processing context
    current_step: str
    steps_completed: list[str]
    errors: list[str]

    # Messages and communication
    messages: list[AnyMessage]


class BaseWorkflowConfig(BaseModel):
    """Configuration for workflow execution."""

    workflow_type: str
    max_retries: int = 3
    timeout_seconds: int = 300
    enable_logging: bool = True
    enable_monitoring: bool = True


class BaseWorkflow:
    """Base class for all LangGraph workflows in YCA Collector."""

    def __init__(self, config: BaseWorkflowConfig):
        self.config = config
        self.graph = StateGraph(WorkflowState)
        self._setup_base_nodes()

    def _setup_base_nodes(self):
        """Setup common nodes that all workflows need."""
        self.graph.add_node("initialize", self._initialize_workflow)
        self.graph.add_node("finalize", self._finalize_workflow)
        self.graph.add_node("error_handler", self._handle_error)

    def _initialize_workflow(self, state: WorkflowState) -> WorkflowState:
        """Initialize workflow state with metadata."""
        if not state.get("workflow_id"):
            state["workflow_id"] = str(uuid.uuid4())

        state.update(
            {
                "workflow_type": self.config.workflow_type,
                "status": "in_progress",
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "current_step": "initialize",
                "steps_completed": ["initialize"],
                "errors": [],
                "messages": [],
            }
        )

        return state

    def _finalize_workflow(self, state: WorkflowState) -> WorkflowState:
        """Finalize workflow execution."""
        state.update(
            {
                "status": "completed",
                "updated_at": datetime.utcnow(),
                "current_step": "completed",
            }
        )

        if self.config.enable_logging:
            print(f"Workflow {state['workflow_id']} completed successfully")

        return state

    def _handle_error(self, state: WorkflowState) -> WorkflowState:
        """Handle workflow errors with cleanup and retry logic."""
        # Perform cleanup operations
        self._cleanup_workflow_resources(state)

        # Check if retry is possible
        retry_count = state.get("retry_count", 0)
        if retry_count < self.config.max_retries:
            state.update(
                {
                    "status": "retrying",
                    "updated_at": datetime.utcnow(),
                    "current_step": "retry",
                    "retry_count": retry_count + 1,
                }
            )

            if self.config.enable_logging:
                print(
                    f"Workflow {state['workflow_id']} retrying (attempt {retry_count + 1}/{self.config.max_retries})"
                )
        else:
            state.update(
                {
                    "status": "failed",
                    "updated_at": datetime.utcnow(),
                    "current_step": "error",
                }
            )

            if self.config.enable_logging:
                print(
                    f"Workflow {state['workflow_id']} failed after {self.config.max_retries} retries"
                )

        return state

    def _cleanup_workflow_resources(self, state: WorkflowState) -> None:
        """Clean up any resources allocated during workflow execution."""
        # Override in subclasses to implement specific cleanup logic
        # Examples: close file handles, cleanup temporary files, release locks
        if self.config.enable_logging:
            print(
                f"Cleaning up resources for workflow {state.get('workflow_id', 'unknown')}"
            )

        # Clear any temporary data that might be consuming memory
        if "temp_data" in state.get("output_data", {}):
            state["output_data"].pop("temp_data", None)

    def compile(self) -> StateGraph:
        """Compile the workflow graph."""
        # Set entry point
        self.graph.set_entry_point("initialize")

        # All workflows end at finalize
        self.graph.add_edge("finalize", "__end__")

        return self.graph.compile()

    async def execute(self, input_data: dict[str, Any]) -> dict[str, Any]:
        """Execute the workflow with given input data."""
        compiled_graph = self.compile()

        initial_state: WorkflowState = {
            "workflow_id": "",
            "workflow_type": self.config.workflow_type,
            "status": "pending",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "input_data": input_data,
            "output_data": {},
            "current_step": "",
            "steps_completed": [],
            "errors": [],
            "messages": [],
        }

        try:
            result = await compiled_graph.ainvoke(initial_state)
            return {
                "success": True,
                "workflow_id": result["workflow_id"],
                "status": result["status"],
                "output_data": result["output_data"],
                "errors": result["errors"],
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "workflow_id": initial_state.get("workflow_id", "unknown"),
                "status": "failed",
            }


def create_workflow_state(
    workflow_type: str, input_data: dict[str, Any]
) -> WorkflowState:
    """Helper function to create initial workflow state."""
    return {
        "workflow_id": str(uuid.uuid4()),
        "workflow_type": workflow_type,
        "status": "pending",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "input_data": input_data,
        "output_data": {},
        "current_step": "created",
        "steps_completed": [],
        "errors": [],
        "messages": [],
    }
