"""
SQLAlchemy database models for the YCA Collector application.
"""

from datetime import datetime

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import declarative_base, relationship

from app.core.utils import generate_uuid

Base = declarative_base()


class TimestampMixin:
    """Mixin that adds timestamp fields to models."""

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )


class Publisher(Base, TimestampMixin):
    """Publisher/Client model."""

    __tablename__ = "publishers"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    name = Column(String(255), nullable=False)
    domain = Column(String(255), nullable=True)
    settings = Column(JSON, nullable=False, default=dict)

    # Relationships
    advertisers = relationship(
        "Advertiser", back_populates="publisher", cascade="all, delete-orphan"
    )
    campaigns = relationship(
        "Campaign", back_populates="publisher", cascade="all, delete-orphan"
    )
    insertion_orders = relationship(
        "InsertionOrder", back_populates="publisher", cascade="all, delete-orphan"
    )


class Advertiser(Base, TimestampMixin):
    """Advertiser/Agency model."""

    __tablename__ = "advertisers"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    publisher_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("publishers.id"), nullable=False
    )
    name = Column(String(255), nullable=False)
    contact_email = Column(String(255), nullable=True)
    contact_name = Column(String(255), nullable=True)
    settings = Column(JSON, nullable=False, default=dict)

    # Relationships
    publisher = relationship("Publisher", back_populates="advertisers")
    campaigns = relationship(
        "Campaign", back_populates="advertiser", cascade="all, delete-orphan"
    )


class Campaign(Base, TimestampMixin):
    """Campaign model."""

    __tablename__ = "campaigns"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    publisher_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("publishers.id"), nullable=False
    )
    advertiser_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("advertisers.id"), nullable=False
    )
    name = Column(String(255), nullable=False)
    io_number = Column(String(100), nullable=True)
    start_date = Column(DateTime, nullable=False)  # Changed to match schema
    end_date = Column(DateTime, nullable=False)  # Changed to match schema
    extracted_data = Column(JSON, nullable=False, default=dict)
    status = Column(String(50), nullable=False, default="active")

    # Relationships
    publisher = relationship("Publisher", back_populates="campaigns")
    advertiser = relationship("Advertiser", back_populates="campaigns")
    insertion_orders = relationship(
        "InsertionOrder", back_populates="campaign", cascade="all, delete-orphan"
    )
    line_items = relationship(
        "LineItem", back_populates="campaign", cascade="all, delete-orphan"
    )


class InsertionOrder(Base, TimestampMixin):
    """Insertion Order model."""

    __tablename__ = "insertion_orders"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    campaign_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=True
    )
    publisher_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("publishers.id"), nullable=False
    )
    advertiser_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("advertisers.id"), nullable=True
    )
    io_number = Column(String(100), nullable=True)
    file_path = Column(String(500), nullable=False)  # Changed to match schema
    file_name = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=True)
    raw_text = Column(String, nullable=True)  # Large text field
    parsed_data = Column(JSON, nullable=False, default=dict)
    status = Column(String(50), nullable=False, default="uploaded")
    processing_errors = Column(JSON, nullable=False, default=list)

    # Relationships
    campaign = relationship("Campaign", back_populates="insertion_orders")
    publisher = relationship("Publisher", back_populates="insertion_orders")
    line_items = relationship(
        "LineItem", back_populates="insertion_order", cascade="all, delete-orphan"
    )


class LineItem(Base, TimestampMixin):
    """Line Item (Creative Specs) model."""

    __tablename__ = "line_items"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    io_id = Column(  # Changed from insertion_order_id to match schema
        PG_UUID(as_uuid=True), ForeignKey("insertion_orders.id"), nullable=False
    )
    campaign_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=False
    )
    name = Column(String(255), nullable=False)
    ad_size = Column(String(50), nullable=True)  # Changed from format to ad_size
    quantity = Column(Integer, nullable=False, default=1)
    specs = Column(
        JSON, nullable=False, default=dict
    )  # Changed from specifications to specs
    status = Column(String(50), nullable=False, default="pending")
    deadline = Column(
        DateTime, nullable=True
    )  # Changed to Date in schema but keep DateTime for compatibility

    # Relationships
    insertion_order = relationship("InsertionOrder", back_populates="line_items")
    campaign = relationship("Campaign", back_populates="line_items")
    creative_assets = relationship(
        "CreativeAsset", back_populates="line_item", cascade="all, delete-orphan"
    )


class CreativeAsset(Base, TimestampMixin):
    """Creative Asset model."""

    __tablename__ = "creative_assets"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    line_item_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("line_items.id"), nullable=False
    )
    campaign_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=False
    )
    file_path = Column(String(500), nullable=False)  # TEXT in schema
    file_name = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_metadata = Column(JSON, nullable=False, default=dict)
    validation_status = Column(String(50), nullable=False, default="pending")
    validation_results = Column(JSON, nullable=False, default=dict)
    uploaded_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    line_item = relationship("LineItem", back_populates="creative_assets")
    campaign = relationship("Campaign")


class AIPersona(Base, TimestampMixin):
    """AI Persona model for communication."""

    __tablename__ = "ai_personas"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String, nullable=True)  # TEXT field
    nationality = Column(String(100), nullable=True)
    age = Column(Integer, nullable=True)
    personality_traits = Column(JSON, nullable=True)  # TEXT[] in schema, using JSON
    communication_style = Column(String, nullable=True)  # TEXT field
    example_phrases = Column(JSON, nullable=True)  # TEXT[] in schema, using JSON
    emoji_usage = Column(String(20), nullable=False, default="moderate")
    formality_level = Column(Integer, nullable=True)  # CHECK constraint in schema
    is_active = Column(Boolean, nullable=False, default=True)


class QALog(Base, TimestampMixin):
    """QA Log model for asset validation."""

    __tablename__ = "qa_logs"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    asset_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("creative_assets.id"), nullable=False
    )
    line_item_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("line_items.id"), nullable=True
    )
    validation_type = Column(String(100), nullable=True)
    validation_results = Column(JSON, nullable=False)
    passed = Column(Boolean, nullable=False, default=False)
    performed_by = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)

    # Note: Only created_at from TimestampMixin is used in schema


class EmailTemplate(Base, TimestampMixin):
    """Email Template model."""

    __tablename__ = "email_templates"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    publisher_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("publishers.id"), nullable=True
    )
    name = Column(String(100), nullable=False, unique=True)
    template_type = Column(String(50), nullable=False)
    subject_template = Column(Text, nullable=False)
    body_template = Column(Text, nullable=False)
    required_variables = Column(JSON, nullable=True)  # TEXT[] in schema, using JSON
    persona_compatible = Column(Boolean, nullable=False, default=True)
    is_active = Column(Boolean, nullable=False, default=True)


class CommunicationLog(Base, TimestampMixin):
    """Communication Log model."""

    __tablename__ = "communication_logs"

    id = Column(
        PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid, index=True
    )
    campaign_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=False
    )
    line_item_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("line_items.id"), nullable=True
    )
    email_template_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("email_templates.id"), nullable=True
    )
    email_type = Column(String(50), nullable=False)
    recipient_email = Column(String(255), nullable=False)
    subject = Column(Text, nullable=True)
    body = Column(Text, nullable=True)
    persona_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("ai_personas.id"), nullable=True
    )
    sent_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    opened_at = Column(DateTime, nullable=True)
    clicked_at = Column(DateTime, nullable=True)
    replied_at = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=True)
    error_message = Column(Text, nullable=True)

    # Note: Only created_at from TimestampMixin is used in schema


class WorkflowState(Base, TimestampMixin):
    """Workflow State model for LangGraph persistence."""

    __tablename__ = "workflow_states"

    workflow_id = Column(PG_UUID(as_uuid=True), primary_key=True, default=generate_uuid)
    io_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("insertion_orders.id"), nullable=True
    )
    current_step = Column(Text, nullable=True)
    status = Column(String(50), nullable=True)
    context = Column(JSON, nullable=False, default=dict)
    errors = Column(JSON, nullable=False, default=list)
