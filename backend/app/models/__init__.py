"""
Pydantic models for YCA Collector API
Data validation and serialization models
"""

from datetime import date, datetime
from typing import Any, Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

# ========================================
# Base Models
# ========================================


class BaseDBModel(BaseModel):
    """Base model for database records"""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime


class APIResponse(BaseModel):
    """Standard API response wrapper"""

    success: bool = True
    message: str = "Success"
    data: Any | None = None
    error: str | None = None


# ========================================
# Publisher & Advertiser Models
# ========================================


class PublisherBase(BaseModel):
    """Publisher base model"""

    name: str = Field(..., max_length=255)
    email: str | None = Field(None, max_length=255)
    contact_person: str | None = Field(None, max_length=255)


class PublisherCreate(PublisherBase):
    """Publisher creation model"""

    pass


class Publisher(BaseDBModel, PublisherBase):
    """Publisher response model"""

    pass


class AdvertiserBase(BaseModel):
    """Advertiser base model"""

    name: str = Field(..., max_length=255)
    publisher_id: UUID
    agency_name: str | None = Field(None, max_length=255)
    agency_email: str | None = Field(None, max_length=255)
    primary_contact_name: str | None = Field(None, max_length=255)
    primary_contact_email: str | None = Field(None, max_length=255)


class AdvertiserCreate(AdvertiserBase):
    """Advertiser creation model"""

    pass


class Advertiser(BaseDBModel, AdvertiserBase):
    """Advertiser response model"""

    pass


# ========================================
# Insertion Order Models
# ========================================


class InsertionOrderBase(BaseModel):
    """Insertion Order base model"""

    advertiser_id: UUID
    io_number: str | None = Field(None, max_length=100)
    io_name: str = Field(..., max_length=255)
    campaign_name: str | None = Field(None, max_length=255)
    flight_start: date | None = None
    flight_end: date | None = None
    total_budget: float | None = Field(None, ge=0)
    currency: str = Field(default="USD", max_length=10)
    notes: str | None = None


class InsertionOrderCreate(InsertionOrderBase):
    """Insertion Order creation model"""

    pass


class InsertionOrderUpdate(BaseModel):
    """Insertion Order update model"""

    io_name: str | None = Field(None, max_length=255)
    campaign_name: str | None = Field(None, max_length=255)
    flight_start: date | None = None
    flight_end: date | None = None
    total_budget: float | None = Field(None, ge=0)
    currency: str | None = Field(None, max_length=10)
    status: (
        Literal["pending", "processing", "processed", "error", "duplicate"] | None
    ) = None
    extraction_status: (
        Literal["pending", "extracted", "failed", "manual_review"] | None
    ) = None
    extracted_data: dict[str, Any] | None = None
    extraction_confidence: float | None = Field(None, ge=0, le=1)
    notes: str | None = None


class InsertionOrder(BaseDBModel, InsertionOrderBase):
    """Insertion Order response model"""

    file_path: str | None = None
    file_name: str | None = None
    file_size: int | None = None
    status: str = "pending"
    extraction_status: str = "pending"
    extracted_data: dict[str, Any] | None = None
    extraction_confidence: float | None = None
    duplicate_of: UUID | None = None


# ========================================
# Line Item Models
# ========================================


class LineItemBase(BaseModel):
    """Line Item base model"""

    io_id: UUID
    line_item_number: str | None = Field(None, max_length=100)
    line_item_name: str = Field(..., max_length=255)
    ad_format: str | None = Field(None, max_length=100)
    dimensions: str | None = Field(None, max_length=50)
    placement_type: str | None = Field(None, max_length=100)
    targeting_criteria: dict[str, Any] | None = None
    budget: float | None = Field(None, ge=0)
    impressions: int | None = Field(None, ge=0)
    clicks: int | None = Field(None, ge=0)
    cpm: float | None = Field(None, ge=0)
    cpc: float | None = Field(None, ge=0)
    start_date: date | None = None
    end_date: date | None = None
    deadline: date | None = None
    specs_requirements: dict[str, Any] | None = None


class LineItemCreate(LineItemBase):
    """Line Item creation model"""

    pass


class LineItemUpdate(BaseModel):
    """Line Item update model"""

    line_item_name: str | None = Field(None, max_length=255)
    ad_format: str | None = Field(None, max_length=100)
    dimensions: str | None = Field(None, max_length=50)
    deadline: date | None = None
    status: (
        Literal[
            "pending",
            "assets_requested",
            "assets_received",
            "assets_approved",
            "assets_rejected",
            "completed",
        ]
        | None
    ) = None
    priority: Literal["low", "normal", "high", "urgent"] | None = None


class LineItem(BaseDBModel, LineItemBase):
    """Line Item response model"""

    status: str = "pending"
    priority: str = "normal"


# ========================================
# Creative Asset Models
# ========================================


class CreativeAssetBase(BaseModel):
    """Creative Asset base model"""

    line_item_id: UUID
    file_name: str = Field(..., max_length=255)
    original_file_name: str | None = Field(None, max_length=255)
    file_path: str = Field(..., max_length=500)
    file_size: int | None = Field(None, ge=0)
    mime_type: str | None = Field(None, max_length=100)
    dimensions: str | None = Field(None, max_length=50)
    duration: int | None = Field(None, ge=0)  # for videos
    file_hash: str | None = Field(None, max_length=64)


class CreativeAssetCreate(CreativeAssetBase):
    """Creative Asset creation model"""

    pass


class CreativeAssetUpdate(BaseModel):
    """Creative Asset update model"""

    validation_status: (
        Literal["pending", "valid", "invalid", "manual_review"] | None
    ) = None
    validation_notes: str | None = None
    validation_results: dict[str, Any] | None = None
    is_approved: bool | None = None
    approved_by: str | None = None
    rejection_reason: str | None = None


class CreativeAsset(BaseDBModel, CreativeAssetBase):
    """Creative Asset response model"""

    validation_status: str = "pending"
    validation_notes: str | None = None
    validation_results: dict[str, Any] | None = None
    version: int = 1
    is_approved: bool = False
    approved_by: str | None = None
    approved_at: datetime | None = None
    rejection_reason: str | None = None


# ========================================
# AI Persona Models
# ========================================


class AIPersonaBase(BaseModel):
    """AI Persona base model"""

    name: str = Field(..., max_length=100)
    description: str | None = None
    tone: Literal["professional", "friendly", "casual", "urgent", "formal"] = (
        "professional"
    )
    style_guidelines: str | None = None
    template_data: dict[str, Any] | None = None
    is_active: bool = True


class AIPersonaCreate(AIPersonaBase):
    """AI Persona creation model"""

    pass


class AIPersona(BaseDBModel, AIPersonaBase):
    """AI Persona response model"""

    pass


# ========================================
# Email Models
# ========================================


class EmailCommunicationBase(BaseModel):
    """Email Communication base model"""

    io_id: UUID
    line_item_id: UUID | None = None
    email_type: Literal[
        "initial_request",
        "reminder",
        "confirmation",
        "rejection",
        "approval",
        "clarification",
    ]
    recipient_email: str = Field(..., max_length=255)
    recipient_name: str | None = Field(None, max_length=255)
    sender_email: str | None = Field(None, max_length=255)
    subject: str | None = None
    body: str | None = None
    html_body: str | None = None
    persona_id: UUID | None = None
    attachments: list[dict[str, Any]] | None = None
    metadata: dict[str, Any] | None = None


class EmailCommunicationCreate(EmailCommunicationBase):
    """Email Communication creation model"""

    pass


class EmailCommunication(BaseDBModel, EmailCommunicationBase):
    """Email Communication response model"""

    thread_id: str | None = None
    message_id: str | None = None
    status: str = "sent"
    opened_at: datetime | None = None
    replied_at: datetime | None = None


# ========================================
# File Upload Models
# ========================================


class FileUploadResponse(BaseModel):
    """File upload response model"""

    file_id: UUID
    file_name: str
    file_path: str
    file_size: int
    mime_type: str
    upload_url: str | None = None


class IOUploadRequest(BaseModel):
    """IO upload request model"""

    advertiser_id: UUID
    io_name: str | None = None
    campaign_name: str | None = None
    notes: str | None = None


class IOUploadResponse(BaseModel):
    """IO upload response model"""

    io_id: UUID
    file_info: FileUploadResponse
    processing_status: str = "queued"
    workflow_id: str | None = None


# ========================================
# Workflow Models
# ========================================


class WorkflowLogBase(BaseModel):
    """Workflow Log base model"""

    io_id: UUID
    workflow_name: str = Field(..., max_length=100)
    workflow_step: str | None = Field(None, max_length=100)
    status: Literal["started", "completed", "failed", "cancelled"]
    input_data: dict[str, Any] | None = None
    output_data: dict[str, Any] | None = None
    error_message: str | None = None
    execution_time: int | None = None  # milliseconds


class WorkflowLogCreate(WorkflowLogBase):
    """Workflow Log creation model"""

    pass


class WorkflowLog(BaseDBModel, WorkflowLogBase):
    """Workflow Log response model"""

    started_at: datetime
    completed_at: datetime | None = None


# ========================================
# AI Extraction Models
# ========================================


class ExtractedIOData(BaseModel):
    """Structure for AI-extracted IO data"""

    campaign_name: str | None = None
    advertiser_name: str | None = None
    agency_name: str | None = None
    flight_start: str | None = None  # Date as string from AI
    flight_end: str | None = None  # Date as string from AI
    total_budget: float | None = None
    currency: str | None = None
    line_items: list[dict[str, Any]] = []
    contact_info: dict[str, str] | None = None
    special_instructions: str | None = None
    extraction_confidence: float | None = None


class AssetValidationResult(BaseModel):
    """Asset validation result model"""

    is_valid: bool
    file_type_valid: bool = True
    dimensions_valid: bool = True
    file_size_valid: bool = True
    format_valid: bool = True
    issues: list[str] = []
    recommendations: list[str] = []
    confidence: float | None = None


# ========================================
# API Request/Response Models
# ========================================


class HealthCheck(BaseModel):
    """Health check response model"""

    status: str = "healthy"
    timestamp: datetime
    version: str = "1.0.0"
    database: str = "connected"
    ai_service: str = "available"
    storage: str = "available"


class ErrorResponse(BaseModel):
    """Error response model"""

    success: bool = False
    error: str
    details: dict[str, Any] | None = None
    timestamp: datetime = Field(default_factory=datetime.now)
