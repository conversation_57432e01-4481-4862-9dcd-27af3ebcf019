"""
Pydantic models for request/response validation.
"""

from datetime import date, datetime
from typing import Any
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field


# Shared properties
class BaseSchema(BaseModel):
    """Base schema with common fields."""

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat(),
            UUID: str,
        }


# Publisher schemas
class PublisherBase(BaseSchema):
    name: str
    email: EmailStr | None = None
    contact_info: dict[str, Any] | None = {}


class PublisherCreate(PublisherBase):
    pass


class PublisherUpdate(BaseSchema):
    name: str | None = None
    email: EmailStr | None = None
    contact_info: dict[str, Any] | None = None


class Publisher(PublisherBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# Advertiser schemas
class AdvertiserBase(BaseSchema):
    name: str
    email: EmailStr | None = None
    contact_person: str | None = None
    publisher_id: UUID


class AdvertiserCreate(AdvertiserBase):
    pass


class AdvertiserUpdate(BaseSchema):
    name: str | None = None
    email: EmailStr | None = None
    contact_person: str | None = None


class Advertiser(AdvertiserBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# Campaign schemas
class CampaignBase(BaseSchema):
    name: str
    advertiser_id: UUID
    start_date: date | None = None
    end_date: date | None = None
    status: str = "pending"


class CampaignCreate(CampaignBase):
    pass


class CampaignUpdate(BaseSchema):
    name: str | None = None
    advertiser_id: UUID | None = None
    start_date: date | None = None
    end_date: date | None = None
    status: str | None = None


class Campaign(CampaignBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# Insertion Order schemas
class InsertionOrderBase(BaseSchema):
    campaign_id: UUID
    file_path: str | None = None
    file_type: str | None = None
    extracted_data: dict[str, Any] | None = {}
    processing_status: str = "pending"


class InsertionOrderCreate(InsertionOrderBase):
    pass


class InsertionOrderUpdate(BaseSchema):
    campaign_id: UUID | None = None
    file_path: str | None = None
    file_type: str | None = None
    extracted_data: dict[str, Any] | None = None
    processing_status: str | None = None


class InsertionOrder(InsertionOrderBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# Extracted Data Schemas
class InsertionOrderExtractedData(BaseSchema):
    """Schema for structured data extracted from an Insertion Order."""

    client_name: str | None = Field(None, description="Name of the client/publisher.")
    advertiser_name: str | None = Field(
        None, description="Name of the advertiser/agency."
    )
    campaign_name: str | None = Field(None, description="Name of the campaign.")
    campaign_start_date: date | None = Field(
        None, description="Start date of the campaign."
    )
    campaign_end_date: date | None = Field(
        None, description="End date of the campaign."
    )
    line_items: list[dict[str, Any]] = Field(
        [], description="List of line items with their specifications."
    )
    # Add other fields as needed based on the PRD/SDD


# Line Item schemas
class LineItemBase(BaseSchema):
    insertion_order_id: UUID
    name: str
    format: str  # '728x90', '300x250', etc.
    specifications: dict[str, Any] = {}
    deadline: date | None = None
    status: str = "pending"


class LineItemCreate(LineItemBase):
    pass


class LineItemUpdate(BaseSchema):
    insertion_order_id: UUID | None = None
    name: str | None = None
    format: str | None = None
    specifications: dict[str, Any] | None = None
    deadline: date | None = None
    status: str | None = None


class LineItem(LineItemBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# Creative Asset schemas
class CreativeAssetBase(BaseSchema):
    line_item_id: UUID
    file_path: str
    file_name: str
    file_type: str
    file_size: int
    dimensions: str
    validation_status: str = "pending"
    validation_notes: str | None = None


class CreativeAssetCreate(CreativeAssetBase):
    pass


class CreativeAssetUpdate(BaseSchema):
    line_item_id: UUID | None = None
    file_path: str | None = None
    file_name: str | None = None
    file_type: str | None = None
    file_size: int | None = None
    dimensions: str | None = None
    validation_status: str | None = None
    validation_notes: str | None = None


class CreativeAsset(CreativeAssetBase):
    id: UUID
    created_at: datetime
    updated_at: datetime


# AI Persona schemas
class AIPersonaBase(BaseSchema):
    name: str
    personality_type: str | None = None
    communication_style: dict[str, Any] = {}
    email_templates: dict[str, str] = {}
    is_active: bool = True


class AIPersonaCreate(AIPersonaBase):
    pass


class AIPersonaUpdate(BaseSchema):
    name: str | None = None
    personality_type: str | None = None
    communication_style: dict[str, Any] | None = None
    email_templates: dict[str, str] | None = None
    is_active: bool | None = None


class AIPersona(AIPersonaBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
