# YCA Collector Backend

FastAPI backend for the YCA Collector application.

## Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd yca-collector
   ```

2. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**

   ```bash
   pip install uv
   uv sync
   ```

4. **Run the application**

   ```bash
   # Development
   uvicorn app.main:app --reload

   # Or using Docker
   docker-compose up --build
   ```

## API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: [http://localhost:8000/docs](http://localhost:8000/docs)
- ReDoc: [http://localhost:8000/redoc](http://localhost:8000/redoc)

## Project Structure

```text
backend/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   └── deps/
│   ├── core/
│   │   ├── config.py
│   │   ├── database.py
│   │   └── security.py
│   ├── models/
│   ├── services/
│   ├── workflows/
│   └── agents/
├── tests/
└── main.py
```

## Environment Variables

See `.env.example` for a list of required environment variables.

## Running Tests

```bash
pytest
```

## Deployment

For production deployment, use the provided Dockerfile and docker-compose.yml:

```bash
docker-compose -f docker-compose.prod.yml up --build -d
```
