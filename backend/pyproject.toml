[project]
name = "yca-collector"
version = "0.1.0"
description = "YourBow Creative Asset Collector"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Web framework
    "fastapi==0.111.0",
    "uvicorn[standard]==0.34.3",
    "python-multipart==0.0.9",
    # Data validation and settings - Updated for compatibility
    "pydantic[email]>=2.9.0,<3.0.0",
    "pydantic-settings>=2.4.0,<3.0.0",
    # AI and LLM frameworks - Updated to compatible versions
    "langchain>=0.3.0,<0.4.0",
    "langgraph>=0.2.0,<1.0.0",
    "langchain-google-genai>=2.1.0,<3.0.0",
    "langchain-community>=0.3.0,<0.4.0",
    "langchain-openai>=0.2.0,<0.3.0",
    # Database and storage
    "supabase==2.15.3",
    "sqlalchemy==2.0.29",
    "psycopg2-binary==2.9.9",
    # Google AI and APIs (google-generativeai and google-auth-httplib2 removed as per diff logic)
    "google-auth==2.29.0",
    "google-auth-oauthlib==1.2.0",
    "google-api-python-client==2.128.0",
    # Security and authentication
    "python-jose[cryptography]==3.3.0",
    # Document processing
    "pypdf>=4.0.0,<5.0.0",
    "python-docx==1.1.2",
    "openpyxl==3.1.2",
    "pillow==10.3.0",
    "unstructured==0.13.2",
    "docx2txt==0.8",
    # Utilities (python-dotenv and aiofiles removed as per diff logic)
    "httpx==0.27.0",
]

[tool.uv]
dev-dependencies = [
    # Testing - Fixed version constraints
    "pytest>=8.0.0,<9.0.0",
    "pytest-asyncio>=0.23.0,<0.27.0",
    "pytest-cov>=5.0.0,<7.0.0",
    "anyio>=4.0.0,<5.0.0",
    # Code quality
    "ruff>=0.11.13,<1.0.0",
    "pyright>=1.1.402,<2.0.0",
    "pre-commit>=3.6.2,<4.0.0",
    # Development utilities
    "ipython>=8.0.0",
]

[tool.ruff]
line-length = 88
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
]
ignore = [
    "E501",   # line too long (handled by formatter)
    "B008",   # do not perform function calls in argument defaults
    "B905",   # zip strict argument
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.pyright]
include = ["app"]
exclude = ["**/__pycache__", "**/node_modules"]
reportMissingImports = true
reportMissingTypeStubs = false
reportOptionalMemberAccess = true
reportOptionalCall = true
reportOptionalIterable = true
reportOptionalContextManager = true
reportOptionalOperand = true
strictListInference = true
strictDictionaryInference = true
pythonVersion = "3.11"
pythonPlatform = "Linux"

[tool.pytest.ini_options]
minversion = "8.0"
addopts = [
    "--strict-config",
]
testpaths = ["app/tests"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "asyncio: marks tests as async",
]

[tool.pytest_asyncio]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
