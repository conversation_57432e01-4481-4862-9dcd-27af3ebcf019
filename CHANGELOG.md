# Changelog

All notable changes to the YCA Collector (YourBow Creative Asset Collector) project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- GitHub issue templates for bug reports and feature requests
- GitHub pull request template with project-specific testing commands
- Dependabot configuration for automated dependency updates
- CodeQL security scanning workflow

### Fixed
- Missing trailing newlines in GitHub template files
- Updated PR template with correct testing commands (`uv run pytest`, `./scripts/quality-check.sh`)

## [0.2.0] - 2025-06-17

### Added
- Comprehensive Makefile for common development tasks
- GitHub Actions CI/CD workflow with automated testing
- Pre-commit hooks with automatic updates
- CodeQL security analysis integration
- Dependabot configuration for dependency management
- GitHub issue and PR templates for better project management

### Changed
- Enhanced CLAUDE.md with comprehensive AI assistant guidelines
- Improved repository structure for production readiness
- Updated development workflow documentation

### Fixed
- AI assistant configuration to prevent common errors
- Repository cleanup and standardization

## [0.1.0] - 2025-06-15

### Added
- Complete YCA Collector FastAPI backend implementation
- AI-powered IO document processing with Google Gemini integration
- Supabase database and storage services integration
- LangGraph workflow orchestration (simplified implementation)
- 10 distinct AI personas for agency communication
- Comprehensive API endpoints for all core functionality:
  - Publishers management (`/api/v1/publishers`)
  - Advertisers management (`/api/v1/advertisers`)
  - Campaigns management (`/api/v1/campaigns`)
  - Insertion orders processing (`/api/v1/insertion-orders`)
  - Line items management (`/api/v1/line-items`)
  - Creative assets handling (`/api/v1/creative-assets`)
  - AI personas management (`/api/v1/ai-personas`)
- Docker containerization with docker-compose setup
- File upload and processing for PDF, DOCX, and XLSX formats
- Automated development setup scripts
- Comprehensive testing framework with pytest
- Type hints and Pydantic models for data validation
- Development tools integration (uv package manager, pre-commit hooks)

### Changed
- Modernized development workflow for AI-assisted coding
- Refactored IO processing architecture for better maintainability
- Enhanced dependency management with uv instead of pip
- Updated project structure for production readiness

### Fixed
- Initial setup command improvements
- Dependency resolution and installation issues

## [0.0.1] - 2025-06-10

### Added
- Initial project structure and repository setup
- Basic project configuration files
- Initial commit with project skeleton

---

## Release Notes

### About YCA Collector

YCA Collector is an AI-powered FastAPI application that automates creative asset collection from agencies. It replaces an n8n proof-of-concept with a production-ready system using LangGraph workflows and AI agents.

### Key Features
- **AI-Powered IO Processing**: Automatically extract structured data from insertion orders
- **Multiple AI Personas**: 10 different communication personalities for agency interactions
- **Modern Tech Stack**: FastAPI, Supabase, Google Gemini, LangGraph
- **Production Ready**: Docker support, comprehensive testing, CI/CD pipelines

### Upgrade Notes

#### From 0.1.x to 0.2.x
- No breaking changes
- New CI/CD pipelines may require environment setup
- Pre-commit hooks now automatically installed

#### From 0.0.x to 0.1.x
- Complete rewrite with new architecture
- Migration from n8n to FastAPI required
- New environment variables needed (see README.md)

[Unreleased]: https://github.com/zipaJopa/yb-creative-collector/compare/v0.2.0...HEAD
[0.2.0]: https://github.com/zipaJopa/yb-creative-collector/compare/v0.1.0...v0.2.0
[0.1.0]: https://github.com/zipaJopa/yb-creative-collector/compare/v0.0.1...v0.1.0
[0.0.1]: https://github.com/zipaJopa/yb-creative-collector/releases/tag/v0.0.1
