# YCA Collector - YourBow Creative Asset Collector

A modern, AI-powered system for automating creative asset collection from agencies. This project replaces the n8n proof-of-concept with a production-ready FastAPI backend using LangGraph workflows and AI agents.

## 🚀 Features

- **AI-Powered IO Processing**: Automatically extract structured data from PDF, DOCX, and XLSX insertion orders
- **LangGraph Workflows**: Orchestrate complex AI workflows for document processing
- **Multiple AI Personas**: 10 different communication personalities for agency interactions
- **Supabase Integration**: Modern database and storage solution
- **FastAPI Backend**: High-performance async API with automatic documentation
- **Docker Support**: Easy deployment and development setup

## 📋 Current Implementation Status

### ✅ **Phase 1 Foundation - COMPLETED** (Dec 2024)
- ✅ FastAPI backend fully operational with real API keys
- ✅ Supabase database connection established  
- ✅ SQLAlchemy models synchronized with database schema
- ✅ All critical dependency conflicts resolved
- ✅ Code quality issues fixed (ruff, pre-commit hooks)
- ✅ Health check endpoints working (`/api/v1/health`)
- ✅ Core CRUD endpoints for publishers, advertisers, campaigns
- ✅ IO document upload endpoint (`/api/v1/io/upload`)
- ✅ Creative asset management endpoints
- ✅ 10 AI personas configured and ready
- ✅ TaskMaster AI project management integrated
- ✅ **LangGraph workflow orchestration system implemented** (Major Phase 2 achievement)

### 🔄 **Currently In Progress (Sprint 2: Dec 30 - Jan 20, 2025):**
- **Document Processing Pipeline**: Building PDF/DOCX/XLSX parsers (Task #60 → YC-121)
- **Sprint Timeline Synchronization**: Updated mapping to Aleksandar's 4-phase Jira structure
- **API Enhancement & Testing**: Comprehensive endpoint validation and integration testing

### 📅 **Sprint 2 Priorities (Phase 2: Communication & AI):**
- **Document Processing**: Parser service, AI extraction, upload endpoints (Tasks #60-62 → YC-121)
- **Email System**: Gmail API, processing service, communication agents (Tasks #63-65 → YC-122)
- **MVP Target**: Q1 2025 delivery with realistic timeline and 3x buffer estimates

### 🎯 **Demo Ready Features:**
- 🟢 Interactive API documentation at `localhost:8000/docs`
- 🟢 System health monitoring with real database connection
- 🟢 Document upload workflow ready for AI processing
- 🟢 Complete backend infrastructure operational

## 🏗️ Architecture

```
YCA Collector/
├── backend/                 # FastAPI backend application
│   ├── app/
│   │   ├── api/            # API route handlers
│   │   ├── agents/         # AI agents and workflows
│   │   ├── core/           # Configuration and settings
│   │   ├── models/         # Pydantic data models
│   │   └── services/       # Business logic services
│   ├── tests/              # Test suite
│   └── Dockerfile          # Container configuration
├── docs/                   # Project documentation
├── scripts/                # Deployment and utility scripts
└── docker-compose.yml      # Local development setup
```

## 🛠️ Technology Stack

- **Backend**: FastAPI (Python 3.11+)
- **AI/ML**: LangChain, LangGraph, Google Gemini
- **Database**: Supabase (PostgreSQL)
- **Storage**: Supabase Storage
- **Containerization**: Docker & Docker Compose
- **Testing**: Pytest
- **Documentation**: Automatic OpenAPI/Swagger

## 🚀 Quick Start

### Makefile (optional)

This project includes a `Makefile` for common development tasks:

```bash
make setup           # Initial setup (Docker, venv, env files)
make dev             # Start development server
make lint            # Run code quality checks
make test            # Run tests with coverage
make quality         # Run lint and tests
make pre-commit      # Run all pre-commit hooks locally
make precommit-autoupdate  # Update pre-commit hook revisions
```

### Prerequisites

- Python 3.11+
- [uv](https://docs.astral.sh/uv/) (modern Python package manager)
- Docker and Docker Compose (optional)
- Supabase account
- Google Gemini API key

### 1. Clone the Repository

```bash
git clone <repository-url>
cd yb-creative-collector
```

### 2. Automated Development Setup

```bash
cd backend/
./scripts/dev-setup.sh
```

This script will:
- Install dependencies with uv
- Set up pre-commit hooks
- Create necessary directories
- Set up environment file from template
- Run initial code quality checks

### 3. Manual Environment Setup (if needed)

```bash
# Copy environment template
cp backend/.env.example backend/.env

# Edit the .env file with your credentials
nano backend/.env
```

Required environment variables:
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
GEMINI_API_KEY=your-gemini-api-key
```

### 4. Daily Development Commands

```bash
cd backend/

# Start development server
./scripts/run-dev.sh

# Run comprehensive quality checks
./scripts/quality-check.sh

# Install new dependency
uv add package-name

# Install dev dependency
uv add --dev package-name

# Run tests
uv run pytest

# Run tests with coverage
uv run pytest --cov=app
```

### 5. Docker Development (Alternative)

```bash
# Start the backend
docker-compose up -d yca-backend

# Optional: Start Langflow for visual prototyping
docker-compose --profile langflow up -d
```

## 📚 API Documentation

Once running, access the interactive API documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

### Key Endpoints

#### IO Processing
- `POST /api/v1/io/upload` - Upload and process IO documents
- `GET /api/v1/io/status/{workflow_id}` - Get processing status
- `GET /api/v1/io/workflows` - List active workflows

#### Asset Management
- `POST /api/v1/assets/upload` - Upload creative assets
- `GET /api/v1/assets/validate/{asset_id}` - Validate assets
- `GET /api/v1/assets/list` - List assets with filtering

## 🧪 Testing

```bash
cd backend/

# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app

# Run specific test file
uv run pytest tests/test_api.py -v

# Run quality checks (includes testing)
./scripts/quality-check.sh
```

## 🤖 AI Coding Assistant Setup

This project is optimized for use with AI coding assistants like Claude Code, Cursor, Windsurf, and Cline.

### Key Files for AI Context
- **`PLANNING.md`** - Project architecture, tech stack, and constraints
- **`TASK.md`** - Current tasks, backlog, and progress tracking
- **`CLAUDE.md`** - Development guidelines and AI assistant rules
- **`backend/DEVELOPMENT.md`** - Quick reference for development commands

### AI Coding Workflow

1. **Start New Conversation**: AI should read `PLANNING.md` for project context
2. **Check Tasks**: Review `TASK.md` for current priorities and add new tasks
3. **Follow Standards**: Use guidelines in `CLAUDE.md` for consistent code quality
4. **Test Everything**: Create unit tests for all new features
5. **Update Documentation**: Keep README.md, TASK.md updated with changes

### Global Rules for AI Assistants
- Never create files longer than 500 lines
- Always add unit tests for new features
- Use type hints and docstrings for all code
- Update task tracking in TASK.md after completing work
- Follow existing patterns and architecture from PLANNING.md

### MCP Server Recommendations
For enhanced AI coding capabilities, consider setting up:
- **Supabase MCP**: Direct database interaction
- **Brave Search MCP**: Web search for documentation
- **Crawl4AI MCP**: Documentation ingestion
- **File System MCP**: Advanced file operations
- **Git MCP**: Version control operations

## 🗄️ Database Schema

The system uses 8 main tables:

- `publishers` - Client/publisher information
- `advertisers` - Agency/advertiser details
- `campaigns` - Campaign information
- `insertion_orders` - IO documents and extracted data
- `line_items` - Creative specifications
- `creative_assets` - Uploaded creative files
- `ai_personas` - Communication personalities
- `communication_logs` - Email interaction history

## 🤖 AI Personas

The system includes 10 distinct AI personas for agency communication:

1. **Viktor Smirov** - Ruthless Collector
2. **Liam Mercer** - The Smooth Talker
3. **Vivian Kao** - The Tiger Mom
4. **Jacky Laurent** - The Femme Fatale Collector
5. **Carl Jenkins** - The Grumpy Paper Pusher
6. **Dante "The Hammer" Cruz** - The Intimidating Enforcer
7. **Daisy Ray Jackson** - Southern Mamma
8. **Riley "Ry" Carter** - The Gen Z Hustler
9. **Blair Kensington** - The Privileged Perfectionist
10. **Felix Porter** - The Polite Business Nerd (Default)

## 📁 File Processing

Supported IO formats:
- **PDF** - Portable Document Format
- **DOCX** - Microsoft Word documents
- **XLSX** - Microsoft Excel spreadsheets

The system automatically:
1. Extracts text from uploaded documents
2. Uses AI to identify key information (advertiser, campaign, line items)
3. Validates extracted data
4. Stores structured information in the database

## 🔧 Configuration

Key configuration options in `backend/app/core/config.py`:

```python
# Application settings
DEBUG = False
LOG_LEVEL = "info"

# Supabase configuration
SUPABASE_URL = "your-supabase-url"
SUPABASE_KEY = "your-supabase-key"

# AI configuration
GEMINI_API_KEY = "your-gemini-key"

# Storage settings
STORAGE_BUCKET = "creative-assets"
```

## 🚀 Deployment

### GCP VM Deployment

1. **Create GCP VM**:
   ```bash
   gcloud compute instances create yca-collector \
     --machine-type=e2-standard-4 \
     --image-family=ubuntu-2204-lts \
     --image-project=ubuntu-os-cloud \
     --boot-disk-size=100GB
   ```

2. **Setup and Deploy**:
   ```bash
   # Copy files to VM
   gcloud compute scp --recurse ./backend/ yca-collector:~/creative-collector/

   # SSH and setup
   gcloud compute ssh yca-collector
   cd ~/creative-collector/backend

   # Install Docker and run
   sudo apt update && sudo apt install docker.io docker-compose
   sudo docker-compose up -d
   ```

### Production Considerations

- Use environment-specific `.env` files
- Set up proper SSL certificates
- Configure nginx reverse proxy
- Implement proper logging and monitoring
- Set up automated backups for Supabase

## 🔍 Monitoring & Debugging

### Logs
```bash
# View application logs
docker-compose logs -f yca-backend

# View specific service logs
docker logs <container-id>
```

### Health Checks
- Application health: `GET /health`
- Database connectivity: Automatic validation on startup
- Storage accessibility: Tested during file operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software developed for YourBow.

## 📞 Support

For technical support or questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in `/docs`

---

**Built with ❤️ by the YourBow Development Team**
