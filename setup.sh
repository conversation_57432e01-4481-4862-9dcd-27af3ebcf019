#!/bin/bash

# YCA Collector Setup Script
# This script sets up the development environment for the YCA Collector application
# Requires: Python 3.11+, <PERSON><PERSON> (optional), uv package manager

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$SCRIPT_DIR/backend"

echo "🚀 Starting YCA Collector setup..."

# Verify requirements
echo "🔍 Checking requirements..."

# Check Python version
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.11"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Python $REQUIRED_VERSION or higher is required (found $PYTHON_VERSION)"
    exit 1
fi

echo "✅ Python $PYTHON_VERSION found"

# Check if uv is installed, install if not
if ! command -v uv &> /dev/null; then
    echo "📦 Installing uv package manager..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    export PATH="$HOME/.cargo/bin:$PATH"

    if ! command -v uv &> /dev/null; then
        echo "❌ Failed to install uv. Please install manually: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
fi

echo "✅ uv package manager found"

# Create and configure environment file
if [ ! -f "$BACKEND_DIR/.env" ]; then
    if [ -f "$BACKEND_DIR/.env.example" ]; then
        echo "📝 Creating .env file from .env.example..."
        cp "$BACKEND_DIR/.env.example" "$BACKEND_DIR/.env"
        echo "⚠️  IMPORTANT: Please update the .env file with your actual configuration:"
        echo "   - Supabase URL and keys"
        echo "   - Google Gemini API key"
        echo "   - Other service credentials"
    else
        echo "❌ .env.example file not found in backend directory"
        exit 1
    fi
else
    echo "✅ .env file already exists"
fi

# Create necessary directories
UPLOAD_DIR="$BACKEND_DIR/uploads"
if [ ! -d "$UPLOAD_DIR" ]; then
    echo "📁 Creating uploads directory..."
    mkdir -p "$UPLOAD_DIR"
    chmod 755 "$UPLOAD_DIR"
    echo "✅ Created uploads directory"
else
    echo "✅ Uploads directory already exists"
fi

# Install Python dependencies
echo "📦 Installing Python dependencies using uv..."
cd "$BACKEND_DIR"
uv sync

echo "🔧 Running code quality checks..."
uv run ruff format .
uv run ruff check . --fix

echo "✨ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. 📝 Update the .env file with your configuration:"
echo "   - Edit: $BACKEND_DIR/.env"
echo ""
echo "2. 🐳 Run with Docker (recommended):"
echo "   docker-compose up --build yca-backend"
echo ""
echo "3. 🐍 Or run directly with Python:"
echo "   cd backend && uv run uvicorn app.main:app --reload --port 8000"
echo ""
echo "4. 🌐 Access the application:"
echo "   - API: http://localhost:8000"
echo "   - Documentation: http://localhost:8000/docs"
echo "   - Health check: http://localhost:8000/health"
echo ""
echo "5. 🧪 Run tests:"
echo "   cd backend && uv run pytest"
echo ""
echo "For more information, see README.md"
