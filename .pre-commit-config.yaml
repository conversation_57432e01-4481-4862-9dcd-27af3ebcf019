repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: debug-statements
      - id: detect-private-key

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.13
    hooks:
      - id: ruff
        args:
          - --fix
          - backend/app/
        files: ^backend/
        pass_filenames: false
        stages: [commit]
      - id: ruff-format
        args:
          - backend/app/
        files: ^backend/
        pass_filenames: false
        stages: [commit]

  - repo: https://github.com/RobertCraigie/pyright-python
    rev: v1.1.402
    hooks:
      - id: pyright
        args: [backend/app/]
        files: ^backend/
        additional_dependencies: [pytest, pytest-asyncio]

  - repo: local
    hooks:
      - id: pytest
        name: Run pytest
        entry: bash -c 'cd backend && uv run pytest app/tests/ --tb=short'
        language: system
        files: ^backend/(app|tests)/.*\.py$
        pass_filenames: false
        always_run: false
