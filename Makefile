## Makefile for YCA Collector project automations

.PHONY: setup dev lint test quality pre-commit precommit-autoupdate

# Initial project-level setup (Docker, venv, env files)
setup:
	@echo "🔧 Running project setup..."
	@bash scripts/dev-setup.sh

# Start backend development server
dev:
	@echo "🚀 Starting development server..."
	@cd backend && bash scripts/run-dev.sh

# Run code quality checks (lint and type-check)
lint:
	@echo "🔍 Running code quality checks..."
	@cd backend && bash scripts/quality-check.sh

# Run tests
test:
	@echo "🧪 Running tests..."
	@cd backend && uv run pytest --cov=app --cov-report=term-missing

# Full quality: lint + tests
quality: lint test

# Run pre-commit hooks locally
pre-commit:
	@echo "🔧 Running pre-commit hooks..."
	@cd backend && uv run pre-commit run --all-files

# Update pre-commit hooks to latest revs
precommit-autoupdate:
	@echo "🔄 Updating pre-commit hook revisions..."
	@pre-commit autoupdate
