version: '3.8'

services:
  yca-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - LOG_LEVEL=info
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/__pycache__
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  langflow:
    image: langflowai/langflow:latest
    ports:
      - "7860:7860"
    environment:
      - LANGFLOW_DATABASE_URL=sqlite:///./langflow.db
    volumes:
      - langflow-data:/app/langflow
    restart: unless-stopped
    profiles:
      - langflow

volumes:
  langflow-data:
