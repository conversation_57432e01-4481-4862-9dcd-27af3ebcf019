#!/usr/bin/env python3
"""
YCA Collector Demo Script
Simple script to test key API endpoints for demo purposes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"


def demo_health_check():
    """Test system health"""
    print("🔧 Testing System Health...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()


def demo_create_publisher():
    """Create a test publisher"""
    print("🏢 Creating Publisher (Client)...")
    data = {
        "name": "Demo Media Company",
        "domain": "demo-media.com",
        "settings": {"contact_email": "<EMAIL>"},
    }
    response = requests.post(f"{BASE_URL}/publishers", json=data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Created Publisher: {response.json()['name']}")
    else:
        print(f"Response: {response.text}")
    print()


def demo_create_advertiser():
    """Create a test advertiser"""
    print("📢 Creating Advertiser (Agency)...")
    data = {
        "name": "Creative Agency Demo",
        "contact_email": "<EMAIL>",
        "contact_name": "John Demo",
        "settings": {"industry": "advertising"},
    }
    response = requests.post(f"{BASE_URL}/advertisers", json=data)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Created Advertiser: {response.json()['name']}")
    else:
        print(f"Response: {response.text}")
    print()


def demo_list_endpoints():
    """Show all available endpoints"""
    print("📋 Available API Endpoints:")
    endpoints = [
        "GET /health - System health check",
        "POST /publishers - Create publisher (client)",
        "GET /publishers - List all publishers",
        "POST /advertisers - Create advertiser (agency)",
        "GET /advertisers - List all advertisers",
        "POST /campaigns - Create campaign",
        "POST /io/upload - 🎯 AI-powered IO document processing",
        "POST /creative-assets - Upload creative files",
        "GET /personas - AI communication personalities",
    ]
    for endpoint in endpoints:
        print(f"  • {endpoint}")
    print()


def main():
    """Run the demo"""
    print("🎬 YCA Collector API Demo")
    print("=" * 50)

    try:
        demo_health_check()
        demo_list_endpoints()
        demo_create_publisher()
        demo_create_advertiser()

        print("✅ Demo completed successfully!")
        print("\n🎯 KEY FEATURES DEMONSTRATED:")
        print("  • Working FastAPI backend")
        print("  • Real Supabase database connection")
        print("  • Publisher/Advertiser management")
        print("  • Ready for AI-powered IO processing")
        print("\n📊 Next Steps: Upload IO documents for AI processing")

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Demo failed: {e}")


if __name__ == "__main__":
    main()
