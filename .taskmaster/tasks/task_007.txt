# Task ID: 7
# Title: Implement Asset Validation Service
# Status: pending
# Dependencies: 3
# Priority: high
# Description: Create service for validating received creative assets
# Details:
1. Implement validation for:
- Image dimensions using Pillow 10.2.0
- Video specs using ffmpeg-python 0.2.0
- File size limits
- File type verification
2. Configure validation rules storage
3. Implement validation reporting

# Test Strategy:
1. Test different file types
2. Verify dimension checking
3. Test size limitations
4. Validate error reporting
5. Test edge cases
