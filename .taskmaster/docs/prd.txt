# Product Requirements Document: YCA Collector v1

**Version:** 1.0
**Author:** YourBow Product & AI Team
**Reference SDD:** [`YCA_Collector_System_Design_Document_v1.pdf`](YCA_Collector_System_Design_Document_v1.pdf:1)

## 1. Introduction

The YourBow Creative Asset Collector (YCA Collector) is an internal automation project designed to streamline the process of collecting creative assets from agencies for various campaigns. This document outlines the product requirements for YCA Collector v1, a robust, scalable, and production-ready system intended to replace the initial n8n Proof of Concept (PoC). This new iteration leverages a bleeding-edge agent stack and workflow automation to enhance efficiency, reliability, and future extensibility.

## 2. Goals and Objectives

The primary goals of YCA Collector v1 are to:

* **Automate Creative Asset Collection:** Minimize manual effort in requesting, receiving, and organizing creative assets from agencies.
* **Improve Data Accuracy and Consistency:** Ensure that collected assets and associated metadata are accurate and consistently stored.
* **Enhance Workflow Reliability:** Implement robust error handling and monitoring to ensure smooth operation and quick issue resolution.
* **Increase Scalability:** Design a system capable of handling a growing volume of campaigns and creative assets without performance degradation.
* **Provide Actionable Insights:** Facilitate better tracking and reporting on asset collection status.
* **Establish a Modern, Extensible Foundation:** Build on a contemporary tech stack that supports future enhancements and integrations.

## 3. Stakeholders

* **Product Owner:** YourBow Dev Team
* **Technical Lead/Architect:** Erhan
* **Operations Team:** Vanja, Pavle
* **Management:** Dragana, Aleksandar
* **End-Users:** YourBow Campaign Managers, Creative Operations Team

## 4. User Stories / Use Cases

### 4.1 IO Ingestion & Processing

* As a Campaign Manager, I want to upload Insertion Orders (IOs) so that the system can automatically extract relevant campaign details.
* As the system, I need to parse various IO formats (XLSX, PDF, DOCX, CSV, {screenshot of a photo of a napkin note}, etc) to extract campaign, client, agency, flight, and line item details.
* As the system, I need to validate extracted IO data against predefined rules to ensure accuracy.

### 4.2 Creative Asset Request & Communication

* As the system, I need to automatically generate and send personalized email requests to agencies for creative assets based on extracted IO data.
* As the system, I need to track the status of asset requests (e.g., sent, opened, received, pending).
* As the system, I need to send automated reminders to agencies for overdue assets.
* As the system, I need to handle incoming email replies and attachments from agencies.

### 4.3 Asset Reception & Validation

* As the system, I need to automatically receive and process creative asset files (images, videos, documents) from agency emails or uploads.
* As the system, I need to perform initial validation of received assets (e.g., file type, dimensions for images, basic spec checking).
* As the system, I need to match received assets to specific line items in the IO.
* As the system, I need to store validated assets in a structured cloud storage solution (e.g., Supabase Storage).

### 4.4 AI Agent Interaction

* As the system, I need to utilize AI agents to intelligently extract information from unstructured text (e.g., email bodies, IO documents).
* As the system, I need to leverage AI agents for persona-driven communication with agencies, adapting tone and style based on predefined personas.
* As the system, I need to use AI agents to assist in matching creative assets to IO line items, even with variations in naming or sizing.

### 4.5 Reporting & Tracking

* As a Campaign Manager, I want to view the real-time status of all creative asset requests and received assets.
* As a Campaign Manager, I want to generate reports on asset collection efficiency and outstanding items.

## 5. Technical Overview / Architecture

YCA Collector v1 will be built upon a modern, modular architecture designed for scalability, maintainability, and extensibility. The core components include:

* **Backend Framework:** **FastAPI** will serve as the primary web framework, providing high performance and asynchronous capabilities for API endpoints and background tasks.
* **AI Workflow Orchestration:** **LangGraph** will be the central engine for orchestrating complex, stateful AI workflows, managing the flow between different AI agents and external tools.
* **AI Agent Framework:** **Pydantic-AI** will be used to develop and integrate various AI agents responsible for tasks such as information extraction, communication, and asset validation.
* **Data Modeling & Validation:** **Pydantic** will be integral for defining structured data models, ensuring data integrity, and validating inputs/outputs for LLM interactions and API requests. Pydantic's role is foundational to the Python/FastAPI/LangChain development.
* **Database & Storage:** **Supabase** (PostgreSQL for relational data, Supabase Storage for creative assets, Supabase Auth for future user management) will serve as the primary backend-as-a-service, replacing Google Sheets and Google Drive for core data persistence.
* **LLM Integration:** Integration with **Gemini models** (e.g. 2.5 Flash, 2.5 Pro) via OpenAI-compatible API endpoints for flexible LLM interaction.
* **Development & Prototyping:** **Langflow** will be utilized as a visual prototyping tool for rapidly designing and testing LangChain agents and flows, which will then be deployed within the LangGraph framework.
* **Asynchronous Processing:** Initial asynchronous operations will leverage FastAPI's `BackgroundTasks`. For future advanced asynchronous tasks and higher throughput, **Celery/Redis** will be integrated in a later phase.
* **Communication:** Integration with Google Workspace APIs (Gmail API for sending/receiving emails, Google Chat Webhooks for notifications).

For a detailed technical architecture, refer to the [`YCA_Collector_System_Design_Document_v1.pdf`](YCA_Collector_System_Design_Document_v1.pdf:1).

## 6. Key Features

### 6.1 IO Ingestion Service

* **Automated Document Parsing:** Ability to ingest and extract structured data from IO document formats such as PDF, XLSX, DOCX, CSV, etc.
* **Data Validation:** Automated validation of extracted fields (e.g., campaign names, dates, line items).

### 6.2 Communication Agent

* **Dynamic Email Generation:** AI-powered generation of personalized email requests and reminders to agencies, adapting to specific campaign details and agency communication history.
* **Persona-Driven Communication:** Utilization of AI personas (stored in Supabase DB) to tailor the tone and style of outgoing communications.
* **Email Processing:** Automated parsing of incoming emails, identifying replies, and extracting attachments.

### 6.3 Asset Management & Validation

* **Automated Asset Ingestion:** Seamless reception of creative assets via email attachments or dedicated upload mechanisms.
* **Initial Asset Validation:** Automated checks for file type, basic image dimensions, and other specified technical requirements.
* **Intelligent Asset Matching:** AI-assisted matching of received assets to specific line items within the IO, even with minor discrepancies in naming conventions.
* **Structured Storage:** Secure and organized storage of all creative assets in Supabase Storage, linked to relevant campaign and line item metadata.

### 6.4 Workflow Automation & Orchestration

* **End-to-End Workflow Automation:** Orchestration of the entire asset collection process from IO ingestion to final asset storage.
* **Error Handling & Notifications:** Robust error handling mechanisms with automated notifications to relevant stakeholders (e.g., Google Chat webhooks) for critical issues.

## 7. Non-Functional Requirements

* **Scalability:** The system must be designed to scale horizontally to accommodate increasing volumes of IOs and creative assets.
* **Performance:** Key operations (IO processing, email generation, asset validation) should complete within acceptable timeframes to ensure efficient workflow.
* **Reliability:** The system must be highly available and resilient to failures, with automated recovery mechanisms.
* **Security:** All data (IOs, creative assets, communication content) must be handled securely, adhering to industry best practices for data encryption, access control, and privacy.
* **Maintainability:** The codebase should be clean, well-documented, and modular to facilitate easy updates and new feature development.
* **Observability:** Implement logging, monitoring, and tracing to provide visibility into system health, performance, and workflow execution.
* **Data Integrity:** Ensure consistency and accuracy of all stored data through robust validation and database constraints.

## 8. Phased Implementation Plan

The development will follow a phased approach, prioritizing core functionalities and iteratively building upon the foundation. This plan aligns with the "Next Steps & Phased Implementation Plan" detailed in the [`YCA_Collector_System_Design_Document_v1.pdf`](YCA_Collector_System_Design_Document_v1.pdf:1).

* **Phase 1: Foundation & Core Backend (MVP)**
  * Set up FastAPI, LangGraph, Supabase.
  * Implement basic IO ingestion and data extraction.
  * Develop core AI agents for initial communication.
  * Establish basic asset storage.
* **Phase 2: Enhanced Communication & Validation**
  * Integrate advanced email sending/receiving capabilities.
  * Refine AI agents for persona-driven communication.
  * Implement comprehensive asset validation rules.
  * Develop intelligent asset matching logic.
* **Phase 3: Reporting & Scalability Enhancements**
  * Build reporting dashboards for tracking collection status.
  * Integrate Celery/Redis for advanced asynchronous processing (if needed).
  * Optimize performance and scalability.

## 9. Future Considerations

* **Advanced AI-Driven Content QA:** Beyond basic spec checks, leverage AI for deeper content analysis (e.g., brand compliance, visual quality).
* **LLM Fine-tuning:** Explore fine-tuning LLMs for highly specialized information extraction or communication tasks.
* **Integration with Ad Servers/DSPs:** Direct integration with advertising platforms for automated asset delivery.
* **User Interface:** Development of a dedicated web-based user interface (using Next.js/MaterialUI) for enhanced user interaction and management.
* **Multi-Tenancy:** Support for multiple clients or internal teams with segregated data and workflows.

## 10. Glossary

* **AI Agent:** An autonomous software entity capable of perceiving its environment, making decisions, and taking actions to achieve goals.
* **FastAPI:** A modern, fast (high-performance) web framework for building APIs with Python 3.7+.
* **IO (Insertion Order):** A document from an advertiser or agency authorizing an ad campaign.
* **LangChain:** A framework for developing applications powered by language models.
* **Langflow:** A UI for LangChain, allowing users to visually build and deploy LangChain applications.
* **LangGraph:** An extension of LangChain for building robust and stateful multi-actor applications with LLMs.
* **Pydantic:** A Python library for data validation and settings management using Python type hints.
* **Supabase:** An open-source Firebase alternative, providing PostgreSQL database, authentication, storage, and more.
* **YCA Collector:** YourBow Creative Asset Collector.
