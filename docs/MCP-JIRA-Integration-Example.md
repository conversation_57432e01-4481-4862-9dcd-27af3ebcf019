# MCP JIRA Integration Examples

This document shows how to use the built-in MCP JIRA tools instead of manual JIRA documentation.

## 🔧 Prerequisites

MCP JIRA integration is already configured in `.claude/settings.local.json`. The following tools are available:

- `mcp__jira__getAccessibleAtlassianResources`
- `mcp__jira__getVisibleJiraProjects` 
- `mcp__jira__searchJiraIssuesUsingJql`
- `mcp__jira__getJiraIssue`
- `mcp__jira__createJiraIssue`
- `mcp__jira__editJiraIssue`
- `mcp__jira__addCommentToJiraIssue`
- `mcp__jira__transitionJiraIssue`

## 🚀 Common JIRA Operations

### 1. Create JIRA Issue from Task Master Task

```javascript
// When a Task Master task is completed, automatically create JIRA issue
const task = await get_task({id: "1.2", projectRoot: "/home/<USER>/yb-creative-collector"});

const jiraIssue = await mcp__jira__createJiraIssue({
  cloudId: "your-cloud-id",
  projectKey: "YCA",
  issueTypeName: "Task",
  summary: task.title,
  description: task.description + "\n\nTask ID: " + task.id
});
```

### 2. Update JIRA Issue When Task Status Changes

```javascript
// When marking task as done, transition JIRA issue
await set_task_status({id: "1.2", status: "done", projectRoot: "/home/<USER>/yb-creative-collector"});

await mcp__jira__transitionJiraIssue({
  cloudId: "your-cloud-id", 
  issueIdOrKey: "YCA-123",
  transition: {id: "31"} // ID for "Done" transition
});
```

### 3. Sync Task Progress to JIRA Comments

```javascript
// When updating task progress, add JIRA comment
await update_subtask({
  id: "1.2.3", 
  prompt: "Implemented authentication service",
  projectRoot: "/home/<USER>/yb-creative-collector"
});

await mcp__jira__addCommentToJiraIssue({
  cloudId: "your-cloud-id",
  issueIdOrKey: "YCA-123", 
  commentBody: "✅ Subtask 1.2.3 completed: Implemented authentication service"
});
```

## 🔄 Automated JIRA-TaskMaster Sync Script

Instead of manual JIRA documentation, you can create a script that automatically syncs TaskMaster with JIRA:

```bash
#!/bin/bash
# scripts/sync-jira.sh

echo "🔄 Syncing TaskMaster tasks with JIRA..."

# Get all completed tasks from TaskMaster
COMPLETED_TASKS=$(task-master list --status=done --format=json)

# For each completed task, check if JIRA issue exists and update
echo "$COMPLETED_TASKS" | jq -r '.[] | .id' | while read task_id; do
  echo "Processing task $task_id..."
  # Add your JIRA sync logic here using MCP tools
done

echo "✅ JIRA sync completed"
```

## 🎯 Benefits of MCP Integration

1. **No Manual Documentation**: Tools are self-documenting through MCP
2. **Real-time Data**: Direct API access instead of static instructions  
3. **Automation Ready**: Can be scripted and automated
4. **Type Safety**: MCP tools provide proper TypeScript definitions
5. **Error Handling**: Built-in error handling and validation

## 📚 Additional Resources

- Run `mcp__jira__getAccessibleAtlassianResources()` to get your cloud ID
- Use `mcp__jira__getVisibleJiraProjects()` to see available projects
- Check `.claude/settings.local.json` for all available JIRA MCP tools

This approach is much more robust than static documentation files and stays automatically up-to-date with JIRA API changes.