<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1076.5546875 937" style="max-width: 1076.5546875px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg"><style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style><style>#export-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#export-svg .error-icon{fill:#ffffff;}#export-svg .error-text{fill:#000000;stroke:#000000;}#export-svg .edge-thickness-normal{stroke-width:2px;}#export-svg .edge-thickness-thick{stroke-width:3.5px;}#export-svg .edge-pattern-solid{stroke-dasharray:0;}#export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#export-svg .edge-pattern-dashed{stroke-dasharray:3;}#export-svg .edge-pattern-dotted{stroke-dasharray:2;}#export-svg .marker{fill:#000000;stroke:#000000;}#export-svg .marker.cross{stroke:#000000;}#export-svg svg{font-family:arial,sans-serif;font-size:14px;}#export-svg p{margin:0;}#export-svg .label{font-family:arial,sans-serif;color:#333;}#export-svg .cluster-label text{fill:#000000;}#export-svg .cluster-label span{color:#000000;}#export-svg .cluster-label span p{background-color:transparent;}#export-svg .label text,#export-svg span{fill:#333;color:#333;}#export-svg .node rect,#export-svg .node circle,#export-svg .node ellipse,#export-svg .node polygon,#export-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#export-svg .rough-node .label text,#export-svg .node .label text,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-anchor:middle;}#export-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#export-svg .rough-node .label,#export-svg .node .label,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-align:center;}#export-svg .node.clickable{cursor:pointer;}#export-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#export-svg .arrowheadPath{fill:#000000;}#export-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#export-svg .flowchart-link{stroke:#000000;fill:none;}#export-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#export-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#export-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#export-svg .cluster text{fill:#000000;}#export-svg .cluster span{color:#000000;}#export-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#export-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#export-svg rect.text{fill:none;stroke-width:0;}#export-svg .icon-shape,#export-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .icon-shape p,#export-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#export-svg .icon-shape rect,#export-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .node .neo-node{stroke:#000000;}#export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node path{stroke:url(#export-svg-gradient);stroke-width:2;}#export-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#export-svg [data-look="neo"].node circle{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node circle .state-start{fill:#000000;}#export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#export-svg-gradient);stroke-width:2;}#export-svg [data-look="neo"].icon-shape .icon{fill:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"><g data-look="neo" data-et="cluster" data-id="subGraph1" id="subGraph1" class="cluster"><rect height="137" width="573.3125" y="8" x="495.2421875" style=""/><g transform="translate(688.5078125, 8)" class="cluster-label"><foreignObject height="21" width="186.78125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Future Components / Phase 2</p></span></div></foreignObject></g></g><g data-look="neo" data-et="cluster" data-id="subGraph0" id="subGraph0" class="cluster"><rect height="734" width="847.6484375" y="195" x="210.90625" style=""/><g transform="translate(571.68359375, 195)" class="cluster-label"><foreignObject height="21" width="126.09375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Backend Application</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mjg4LjE1NjI1LCJ5IjoxMjB9LHsieCI6Mjg4LjE1NjI1LCJ5IjoxNDV9LHsieCI6Mjg4LjE1NjI1LCJ5IjoxNzB9LHsieCI6Mjg4LjE1NjI1LCJ5IjoxOTV9LHsieCI6MzM3LjQ5MjE4NzUsInkiOjIyMS4zMjY5MTI1NDMzNjMxNn1d" data-id="L_A_B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 116.10337829589844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M288.15625,120L288.15625,145L288.15625,170L288.15625,185.27990474142132Q288.15625,195 296.73176420865286,199.57611274916124L333.96320389211434,219.44375702793855"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDE2LjUyMzQzNzUsInkiOjMwN30seyJ4Ijo0MTYuNTIzNDM3NSwieSI6MzMyfSx7IngiOjQ3NS45NTczNTIzMTM0NzEwNiwieSI6NDAyLjg1NTE0NzY4NjUyODk0fV0=" data-id="L_B_C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 103.48323822021484 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M416.5234375,307L416.5234375,319.5Q416.5234375,332 424.55664751819035,341.5769273153578L473.38672510765014,399.7905309456144"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDU2LjQ2Njk1Njg0MTI1NDA1LCJ5Ijo0OTEuNjU0NDU2ODQxMjU0MDV9LHsieCI6Mjg3Ljg0NzY1NjI1LCJ5Ijo1ODF9LHsieCI6Mjg3Ljg0NzY1NjI1LCJ5Ijo2Mzl9LHsieCI6Mjg3Ljg0NzY1NjI1LCJ5Ijo2OTd9LHsieCI6NTM3LjgzNTkzNzUsInkiOjczOC41NjA2ODEyMTUwNzk5fV0=" data-id="L_C_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 543.0465698242188 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M456.46695684125405,491.65445684125405L296.4156222344893,576.4601307681526Q287.84765625,581 287.84765625,590.696414479262L287.84765625,639L287.84765625,689.2663992495725Q287.84765625,697 295.4765472391249,698.2683070775887L533.8900958485788,737.9046829971637"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjM2LjcxODc1LCJ5Ijo3ODh9LHsieCI6NjM2LjcxODc1LCJ5Ijo4MTN9LHsieCI6NjM2LjcxODc1LCJ5Ijo4Mzh9XQ==" data-id="L_D_E_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M636.71875,788L636.71875,813L636.71875,834"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDc1LjYyNTY3MTMzNTk2OTIsInkiOjUxMC44MTMxNzEzMzU5NjkyfSx7IngiOjQxNS4xMTMyODEyNSwieSI6NTgxfSx7IngiOjQxNS4xMTMyODEyNSwieSI6NjA2fV0=" data-id="L_C_F_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 103.80030059814453 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_F_0" d="M475.6256713359692,510.8131713359692L421.96956998061074,573.0475598183639Q415.11328125,581 415.11328125,591.5L415.11328125,602"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTY5LjI4NDA5NzU1NjIyLCJ5Ijo1MDguNTI4NDAyNDQzNzgwMDd9LHsieCI6NjM2LjcxODc1LCJ5Ijo1ODF9LHsieCI6NjM2LjcxODc1LCJ5Ijo2MDZ9XQ==" data-id="L_C_G_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 110.0284652709961 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_G_0" d="M569.28409755622,508.52840244378007L629.5660661759038,573.3130556062561Q636.71875,581 636.71875,591.5L636.71875,602"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTk0LjAxOTEyMzg1OTQxMTksInkiOjQ4My43OTMzNzYxNDA1ODh9LHsieCI6ODU4LjY1NjI1LCJ5Ijo1ODF9LHsieCI6ODU4LjY1NjI1LCJ5Ijo2MDZ9XQ==" data-id="L_C_H_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 291.8709716796875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_H_0" d="M594.0191238594119,483.793376140588L850.4562635952493,577.9879774401894Q858.65625,581 858.65625,589.7356772455777L858.65625,602"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" marker-start="url(#export-svg_flowchart-v2-pointStart-margin)" data-points="W3sieCI6NDE1LjExMzI4MTI1LCJ5Ijo2NzJ9LHsieCI6NDE1LjExMzI4MTI1LCJ5Ijo2OTd9LHsieCI6NTM3LjgzNTkzNzUsInkiOjcyOS4xMTk3NTgxNTY5MTZ9XQ==" data-id="L_F_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 9 123.63641357421875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_D_0" d="M415.11328125,676L415.11328125,688.8175711181037Q415.11328125,697 423.029081614473,699.0717738769226L533.9662794907568,728.1069665241007"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" marker-start="url(#export-svg_flowchart-v2-pointStart-margin)" data-points="W3sieCI6NjM2LjcxODc1LCJ5Ijo2NzJ9LHsieCI6NjM2LjcxODc1LCJ5Ijo2OTd9LHsieCI6NjM2LjcxODc1LCJ5Ijo3MjJ9XQ==" data-id="L_G_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 9 24 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_D_0" d="M636.71875,676L636.71875,697L636.71875,718"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" marker-start="url(#export-svg_flowchart-v2-pointStart-margin)" data-points="W3sieCI6ODU4LjY1NjI1LCJ5Ijo2NzJ9LHsieCI6ODU4LjY1NjI1LCJ5Ijo2OTd9LHsieCI6NzM1LjYwMTU2MjUsInkiOjcyOS4xNTg0NzY0ODU0OTcxfV0=" data-id="L_H_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 9 123.966796875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_D_0" d="M858.65625,676L858.65625,688.8195128449378Q858.65625,697 850.7415695166488,699.0683817202337L739.4715914277778,728.1471031132139"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTc1LjkwNjI1LCJ5IjoyNzcuMzU4NzU3OTY2MjgwNH0seyJ4Ijo1MDYuOTEwMTU2MjUsInkiOjMzMn0seyJ4Ijo1MTEuMzE3ODQ4NDIzNjk2OCwieSI6MzY3LjQ5NDY1MTU3NjMwMzJ9XQ==" data-id="L_I_C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 356.084716796875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_C_0" d="M175.90625,277.3587579662804L498.66965818856454,330.6396805579895Q506.91015625,332 507.9393971520224,340.2883617470787L510.82491827611796,363.5251402877933"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" marker-start="url(#export-svg_flowchart-v2-pointStart-margin)" data-points="W3sieCI6NjA4LjA4NTkzNzUsInkiOjEwOS41fSx7IngiOjYwOC4wODU5Mzc1LCJ5IjoxNDV9LHsieCI6NjA4LjA4NTkzNzUsInkiOjE3MH0seyJ4Ijo2MDguMDg1OTM3NSwieSI6MTk1fSx7IngiOjQ5NS41NTQ2ODc1LCJ5IjoyMzUuMjM5NTU5NTQzMjMwMDN9XQ==" data-id="L_J_B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 9 176.940185546875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_B_0" d="M608.0859375,113.5L608.0859375,145L608.0859375,170L608.0859375,186.31775546723827Q608.0859375,195 599.9106511456434,197.92336503894774L499.3211266153729,233.89273498517173"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" marker-start="url(#export-svg_flowchart-v2-pointStart-margin)" data-points="W3sieCI6Nzk2LjY2NDA2MjUsInkiOjEwOS41fSx7IngiOjc5Ni42NjQwNjI1LCJ5IjoxNDV9LHsieCI6Nzk2LjY2NDA2MjUsInkiOjE3MH0seyJ4Ijo3OTYuNjY0MDYyNSwieSI6MTk1fSx7IngiOjc5Ni42NjQwNjI1LCJ5IjoyNjMuNX0seyJ4Ijo3OTYuNjY0MDYyNSwieSI6MzMyfSx7IngiOjU4OS44NzAzMjAyNzUxNDg3LCJ5Ijo0MjYuMDU3ODIwMjc1MTQ4OH1d" data-id="L_K_C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 9 421.7535705566406 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_C_0" d="M796.6640625,113.5L796.6640625,145L796.6640625,170L796.6640625,195L796.6640625,263.5L796.6640625,322.76269726201787Q796.6640625,332 788.2556612211573,335.8244672579443L593.5113836969499,424.4017233098653"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTMzLjQyNjgzNjIyMjYyNzcsInkiOjEwOS41fSx7IngiOjg5My41NzAzMTI1LCJ5IjoxNDV9LHsieCI6ODkzLjU3MDMxMjUsInkiOjE3MH0seyJ4Ijo4OTMuNTcwMzEyNSwieSI6MTk1fSx7IngiOjg5My41NzAzMTI1LCJ5IjoyNjMuNX0seyJ4Ijo4OTMuNTcwMzEyNSwieSI6MzMyfSx7IngiOjU5NS45MDAzNzI1NTEwMjIsInkiOjQzMi4wODc4NzI1NTEwMjIwM31d" data-id="L_L_C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 537.9049682617188 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_C_0" d="M933.4268362226277,109.5L902.6947764372877,136.87288705788916Q893.5703125,145 893.5703125,157.21907553445394L893.5703125,170L893.5703125,195L893.5703125,263.5L893.5703125,323.4332282239993Q893.5703125,332 885.450262132212,334.73026751192396L599.691789238711,430.8130551129889"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTg5LjI4MzcwMjA5ODU0MDIsInkiOjEwOS41fSx7IngiOjEwMDkuNTE1NjI1LCJ5IjoxNDV9LHsieCI6MTAwOS41MTU2MjUsInkiOjE3MH0seyJ4IjoxMDA5LjUxNTYyNSwieSI6MTk1fSx7IngiOjEwMDkuNTE1NjI1LCJ5IjoyNjMuNX0seyJ4IjoxMDA5LjUxNTYyNSwieSI6MzMyfSx7IngiOjEwMDkuNTE1NjI1LCJ5Ijo0NTYuNX0seyJ4IjoxMDA5LjUxNTYyNSwieSI6NTgxfSx7IngiOjEwMDkuNTE1NjI1LCJ5Ijo2Mzl9LHsieCI6MTAwOS41MTU2MjUsInkiOjY5N30seyJ4Ijo3MzUuNjAxNTYyNSwieSI6NzM5LjYxNTc0MjQ4NzExMTh9XQ==" data-id="L_L_D_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 854.1201171875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_D_0" d="M989.2837020985402,109.5L1003.3262974639866,134.13987916034324Q1009.515625,145 1009.515625,157.5L1009.515625,170L1009.515625,195L1009.515625,263.5L1009.515625,332L1009.515625,456.5L1009.515625,581L1009.515625,639L1009.515625,689.3134606513823Q1009.515625,697 1001.9204578920106,698.1816614403309L739.5540132784405,739.0008174152491"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_B_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_B_C_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D_E_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_F_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_G_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_H_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_F_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_G_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_H_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_I_C_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_J_B_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_K_C_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_L_C_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_L_D_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(288.15625, 76.5)" data-look="neo" data-et="node" data-node="true" data-id="A" id="flowchart-A-0" class="node default"><rect stroke="url(#gradient)" height="87" width="168.921875" y="-43.5" x="-84.4609375" data-id="A" style="fill:#lightgrey !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-68.4609375, -31.5)" style="" class="label"><rect/><foreignObject height="63" width="136.921875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>External Sources:<br />Email (IOs/Creatives),<br />API (Future UI)</p></span></div></foreignObject></g></g><g transform="translate(416.5234375, 263.5)" data-look="neo" data-et="node" data-node="true" data-id="B" id="flowchart-B-1" class="node default"><rect stroke="url(#gradient)" height="87" width="158.0625" y="-43.5" x="-79.03125" ry="3" data-id="B" rx="3" style="fill:#lightblue !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.03125, -31.5)" style="" class="label"><rect/><foreignObject height="63" width="126.0625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>FastAPI:<br />API Gateway &amp;<br />IO Ingestion Service</p></span></div></foreignObject></g></g><g transform="translate(520.8125, 456.5)" data-look="neo" data-et="node" data-node="true" data-id="C" id="flowchart-C-3" class="node default"><polygon style="fill:#lightblue !important;stroke:#333 !important;stroke-width:2px !important" transform="translate(-99.5,99.5)" class="label-container" points="99.5,0 199,-99.5 99.5,-199 0,-99.5"/><g transform="translate(-69.5, -21)" style="" class="label"><rect/><foreignObject height="42" width="139"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Workflow Orchestrator<br />(LangGraph)</p></span></div></foreignObject></g></g><g transform="translate(636.71875, 755)" data-look="neo" data-et="node" data-node="true" data-id="D" id="flowchart-D-5" class="node default"><rect stroke="url(#gradient)" height="66" width="197.765625" y="-33" x="-98.8828125" data-id="D" style="fill:#lightblue !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-82.8828125, -21)" style="" class="label"><rect/><foreignObject height="42" width="165.765625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>AI Agent Service<br />(LangChain Agents, LLMs)</p></span></div></foreignObject></g></g><g transform="translate(636.71875, 871)" data-look="neo" data-et="node" data-node="true" data-id="E" id="flowchart-E-7" class="node default"><rect stroke="url(#gradient)" height="66" width="137.03125" y="-33" x="-68.515625" data-id="E" style="fill:#lightyellow !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-52.515625, -21)" style="" class="label"><rect/><foreignObject height="42" width="105.03125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>LLM Providers<br />(Gemini, via API)</p></span></div></foreignObject></g></g><g transform="translate(415.11328125, 639)" data-look="neo" data-et="node" data-node="true" data-id="F" id="flowchart-F-9" class="node default"><rect stroke="url(#gradient)" height="66" width="184.53125" y="-33" x="-92.265625" data-id="F" style="fill:#lightgreen !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-76.265625, -21)" style="" class="label"><rect/><foreignObject height="42" width="152.53125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Database Service<br />(Supabase PostgreSQL)</p></span></div></foreignObject></g></g><g transform="translate(636.71875, 639)" data-look="neo" data-et="node" data-node="true" data-id="G" id="flowchart-G-11" class="node default"><rect stroke="url(#gradient)" height="66" width="158.0625" y="-33" x="-79.03125" data-id="G" style="fill:#lightgreen !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-63.03125, -21)" style="" class="label"><rect/><foreignObject height="42" width="126.0625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>File Storage Service<br />(Supabase Storage)</p></span></div></foreignObject></g></g><g transform="translate(858.65625, 639)" data-look="neo" data-et="node" data-node="true" data-id="H" id="flowchart-H-13" class="node default"><rect stroke="url(#gradient)" height="66" width="185.8125" y="-33" x="-92.90625" data-id="H" style="fill:#lightgreen !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-76.90625, -21)" style="" class="label"><rect/><foreignObject height="42" width="153.8125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Email Service<br />(Google Workspace API)</p></span></div></foreignObject></g></g><g transform="translate(91.953125, 263.5)" data-look="neo" data-et="node" data-node="true" data-id="I" id="flowchart-I-20" class="node default"><rect stroke="url(#gradient)" height="66" width="167.90625" y="-33" x="-83.953125" data-id="I" style="fill:#whitesmoke !important;stroke:#333 !important;stroke-width:2px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-67.953125, -21)" style="" class="label"><rect/><foreignObject height="42" width="135.90625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Workflow Prototyping:<br />Langflow</p></span></div></foreignObject></g></g><g transform="translate(608.0859375, 76.5)" data-look="neo" data-et="node" data-node="true" data-id="J" id="flowchart-J-22" class="node default"><rect stroke="url(#gradient)" height="66" width="155.6875" y="-33" x="-77.84375" data-id="J" style="fill:#lightcoral !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-61.84375, -21)" style="" class="label"><rect/><foreignObject height="42" width="123.6875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Frontend<br />(Next.js, MaterialUI)</p></span></div></foreignObject></g></g><g transform="translate(796.6640625, 76.5)" data-look="neo" data-et="node" data-node="true" data-id="K" id="flowchart-K-24" class="node default"><rect stroke="url(#gradient)" height="66" width="121.46875" y="-33" x="-60.734375" data-id="K" style="fill:#lightgrey !important;stroke:#333 !important;stroke-width:2px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-44.734375, -21)" style="" class="label"><rect/><foreignObject height="42" width="89.46875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Task Queue<br />(Celery/Redis)</p></span></div></foreignObject></g></g><g transform="translate(970.4765625, 76.5)" data-look="neo" data-et="node" data-node="true" data-id="L" id="flowchart-L-26" class="node default"><rect stroke="url(#gradient)" height="66" width="126.15625" y="-33" x="-63.078125" data-id="L" style="fill:#lightgrey !important;stroke:#333 !important;stroke-width:2px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-47.078125, -21)" style="" class="label"><rect/><foreignObject height="42" width="94.15625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Observability<br />(OpenLLMetry)</p></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#000000" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#000000" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="export-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>
