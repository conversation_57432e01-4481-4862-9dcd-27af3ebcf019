# System Design Document: YourBow Creative Asset Collector v1

**Version:** 1.0
**Author:** YourBow Product & AI Team
**Status:** Proposal for Review

## 1. Introduction and Goals

### 1.1. Overview

This document details the proposed system architecture and technology stack for the YourBow Creative Asset Collector (YCA Collector) v1. It serves as the formal "Tech Stack schema" requested and outlines the technical evolution from the existing n8n-based proof-of-concept (PoC) workflow into a production-ready, scalable, and AI-enhanced full-stack application. The core objective remains to streamline and automate the creative asset collection, Quality Assurance (QA), and management processes for publishers interacting with agencies/advertisers.

### 1.2. Project Goals

* **Automate and Enhance Core Workflow:** Faithfully replicate the functionalities of Vanja's n8n PoC and introduce improvements in reliability, error handling, and user experience (in future frontend phases).
* **Scalability and Performance:** Engineer a backend capable of supporting a significant increase in clients, campaigns, assets, and concurrent operations.
* **Advanced and Flexible AI Capabilities:** Utilize state-of-the-art Language Models (LLMs) for sophisticated information extraction from Insertion Orders (IOs), dynamic and persona-driven email communications, and foundational logic for asset validation.
* **Modularity and Maintainability:** Design a system with well-defined, loosely coupled components to facilitate easier development, testing, maintenance, and future upgrades.
* **Clear Development Blueprint:** Provide a precise technical specification for the development team (Ilija and Uroš) to effectively build the application.
* **Foundation for Future Growth:** Establish a technical architecture that can support future SaaS productization, advanced AI-driven QA, and comprehensive analytics.

### 1.3. Scope

* **In Scope (Phase 1 - Backend & Core Logic):**
  * Backend services for IO ingestion (email, API), advanced data extraction (beyond current n8n capabilities if feasible), and structured data storage.
  * Workflow orchestration engine to manage the asset collection lifecycle.
  * AI agent framework for persona-based email generation and communication management.
  * Robust database schema for all project entities.
  * Secure file storage for IOs and creative assets.
  * Well-defined API endpoints for all backend services to enable future frontend integration or external system connections.
  * Replication of all core n8n PoC functionalities (GDrive folder creation, Sheets updates, email sequences, persona logic, basic asset validation like file type/dimensions).
* **Out of Scope (for this initial backend-focused phase, but part of the overall Product Vision):**
  * Full-fledged interactive frontend/UI for publishers or agencies (the defined frontend stack of Next.js/MaterialUI is for a subsequent phase).
  * Deep AI-powered creative content analysis (e.g., brand compliance, sentiment in ad copy, visual defect detection).
  * Advanced, user-facing reporting dashboards.
  * Direct client/agency login portal (initial interactions will be email-centric, mirroring n8n).

## 2. System Architecture

### 2.1. Architectural Principles

* **Service-Oriented/Modular Monolith:** The backend will be built using Python FastAPI, initially as a well-structured monolith with clearly delineated services/modules. This facilitates rapid development while allowing for future extraction into microservices if and when scale demands.
* **AI-Centric Workflow:** LangGraph will serve as the backbone for orchestrating stateful, multi-step AI agent workflows.
* **Data Integrity:** Supabase (PostgreSQL) will ensure relational data integrity, complemented by Supabase Storage for unstructured file data.
* **Open Standards & Flexibility:** Prioritize OpenAI-compatible API endpoints for LLM interactions to avoid vendor lock-in and leverage best-in-class models.

### 2.2. High-Level Architecture Diagram

![System Design Chart](./System_design_chart.svg)

### 2.3. Component Deep Dive

#### 2.3.1. IO Ingestion Service (FastAPI)

* **Responsibilities:**
  * Receives IOs: Exposes API endpoints for direct uploads (future UI) and integrates with a mechanism to poll/receive IOs from designated email inboxes (e.g., `<EMAIL>`).
  * Initial Parsing & Validation: Basic validation of file types (PDF, DOCX, XLSX).
  * Secure Storage: Uploads raw IO documents to Supabase Storage (`/tenant_id/advertiser_id/ios/`).
  * Workflow Initiation: Triggers the LangGraph-based "IO Processing Workflow".
* **Technologies:** Python, FastAPI.

#### 2.3.2. Workflow Orchestration (LangGraph)

* **Responsibilities:**
  * Manages the end-to-end stateful process for each IO, from extraction to final asset confirmation.
  * Defines the sequence of operations, conditional branching (e.g., if IO data is invalid, if assets are missing), and error handling logic.
  * Coordinates calls to the AI Agent Service, Database Service, File Storage Service, and Email Service.
  * Replicates and enhances the logic flow demonstrated in Vanja's n8n workflow.
* **Technologies:** Python, Langchain LangGraph. State can be persisted within LangGraph's memory or backed by Supabase DB for long-running/resumable flows.

#### 2.3.3. AI Agent Service (LangChain Agents interacting with LLMs)

* **Responsibilities:** Houses specialized AI agents built with LangChain:
  * **IO Data Extraction Agent:**
    * Input: IO document (path from Supabase Storage or raw text).
    * Process: Uses an LLM (e.g., Gemini 2.5 Pro, Gemini 2.5 Flash) with structured output capabilities (leveraging Pydantic models) to parse the IO and extract key information (Advertiser, Campaign Name, Line Items with specs, Flight Dates, etc.).
    * Output: Structured JSON data.
    * This replaces the "Information Extractor1" and "Extract_Text_From_IO_File" nodes in n8n.
  * **Communication Agent (Persona-driven):**
    * Input: Campaign data, line item status, target contact, predefined persona.
    * Process: Generates contextually appropriate emails (reminders, confirmations, QA feedback) using an LLM, incorporating the selected persona's tone and style. Persona definitions and email templates will be stored and retrieved from Supabase DB.
    * Output: Email content (subject, body).
    * This replaces the "AI Agent_Send_Creative_Reminder" and "Personalities" logic in n8n.
  * **Asset Validation Agent (Initial Version):**
    * Input: Creative asset file, required specs (from IO data).
    * Process: For image/video files, extracts metadata (dimensions, format, file size) using appropriate libraries (e.g., Pillow, FFmpeg). Compares against required specs.
    * Output: Validation status (Pass/Fail), reasons for failure.
    * This replaces "Image dimensions", "Set Image Info", and parts of "The_Asset_Analyzer" from n8n. Advanced visual AI QA is a future enhancement.
* **LLM Interaction:** Agents will use OpenAI-compatible APIs to communicate with selected LLMs.
* **Technologies:** Python, LangChain, Pydantic (for structured LLM outputs).

#### 2.3.4. Database Service (Supabase PostgreSQL)

* **Responsibilities:**
  * Stores and manages all structured data: Publisher profiles, specs, IO details, line items, campaign timelines, creative asset metadata, QA status, communication logs, AI persona definitions.
  * Provides relational integrity and querying capabilities.
* **Key Tables (Illustrative):** `Publishers`, `Advertisers`, `Users` (Supabase Auth), `IOSpecs`, `Campaigns`, `InsertionOrders`, `LineItems`, `CreativeAssets`, `QALogs`, `EmailTemplates`, `AIPersonas`.
* This replaces all Google Sheets functionalities from the n8n workflow.

#### 2.3.5. File Storage Service (Supabase Storage)

* **Responsibilities:**
  * Securely stores all uploaded IO documents and creative assets.
  * Organizes files in a structured manner (e.g., by Publisher/Advertiser/Campaign/AssetType).
* This replaces all GDrive functionalities from the n8n workflow.

#### 2.3.6. Email Service (Google Workspace API)

* **Responsibilities:**
  * Sends emails generated by the Communication Agent.
  * Potentially handles incoming email replies for asset submission (requires parsing and routing back to the Ingestion Service).

#### 2.3.7. Development & Prototyping Tool (Langflow)

* **Role:** Langflow can be used by the AI team (Pavle, Vanja) and potentially developers (Uroš, Ilija) to:
  * Visually design, build, and test LangChain agents and chains before full backend integration.
  * Experiment with different prompts, LLMs, and LangChain tools for tasks like IO extraction and email generation.
  * Export prototyped flows as Python code, which can then be integrated into the LangGraph orchestrator.
* **Benefit:** This addresses Erhan's suggestion for a fully open-source, visual alternative to n8n *for the AI workflow development aspect*, streamlining the creation of sophisticated agents. It acts as a bridge between n8n's visual paradigm and the code-based LangGraph backend.

### 2.6. Technology Stack Rationale

* **Python (FastAPI, LangGraph, LangChain, Pydantic):** Chosen for its dominant AI/ML ecosystem, developer productivity, high-performance web framework (FastAPI), and specialized libraries for AI agent and workflow creation (LangGraph, LangChain). Pydantic ensures data integrity.
* **Supabase (PostgreSQL, Auth, Storage):** Offers an integrated, open-source-based backend-as-a-service solution, simplifying infrastructure for database, user authentication, and file storage. It's a direct, more robust replacement for Google Sheets/GDrive.
* **OpenAI-Compatible API Endpoints for LLMs:** Provides crucial flexibility to leverage various state-of-the-art models (OpenAI, Gemini, Claude, etc.) without vendor lock-in, optimizing for cost and performance per task.
* **Google Workspace:** Leverages existing YourBow email infrastructure for outbound communications.
* **Langflow:** Included as Erhan's suggestion for a visual, open-source tool to aid in the development and prototyping of LangChain agents, making the transition from n8n smoother and fostering easier collaboration on AI component design.
* **Next.js with MaterialUI (Frontend - Future):** A modern, performant stack for building user-friendly interfaces.
* **Celery with Redis (Queue System - Future/Phase 2):** For robust, scalable background task processing as the system grows.

## 3. Key Technical Implementation Details

* **IO Processing Logic:** The n8n workflow's logic for GDrive folder creation, duplicate checks, deadline calculation, and Sheet updates will be translated into LangGraph sequences and Supabase database operations.
* **Persona Implementation:** AI Personas defined in `AI_Agent_Personas.pdf` (and currently in a Google Sheet) will be stored in the Supabase DB. The Communication Agent will fetch and apply these personas when generating emails.
* **State Persistence:** LangGraph can manage in-memory state for individual workflow runs. For longer-term persistence or recovery, critical state checkpoints can be saved to Supabase DB.
* **API Security:** All FastAPI endpoints will be secured, likely using Supabase Auth for publisher/admin users and API keys for service-to-service communication.
* **Error Handling & Logging:** Comprehensive error handling will be implemented within LangGraph and FastAPI services. Logging will be directed to a chosen system (e.g., Google Cloud Logging, or a simpler file/DB log initially).

## 4. Migration from n8n

The migration will involve:

1. **Detailed Analysis:** Breaking down each node and connection in Vanja's n8n workflow.
2. **Component Mapping:** Assigning each n8n function to a corresponding component in the new architecture (e.g., FastAPI endpoint, LangGraph node, Supabase query, LangChain agent call).
3. **Data Model Translation:** Migrating the implicit data structures in n8n (like JSON objects passed between nodes) to formal Pydantic models and Supabase table schemas.
4. **Logic Replication:** Re-implementing the conditional logic, loops, and AI interactions within LangGraph and Python services.
5. **Phased Rollout:** Starting with the core IO ingestion and processing, then adding communication, asset validation, etc.

## 5. Observability and Testing (Future - aligning with Erhan's advice)

* **Observability:** Plan for future integration with OpenLLMetry or a similar tool to track LLM call performance, token usage, costs, and overall workflow health.
* **AI-driven Testing:** Explore creating separate AI agents to test the main Collector workflow by generating mock IOs and simulating agency responses.

## 6. Next Steps & Phased Implementation Plan

(Refined from PRD and email action items)

#### **Phase 1: Foundation & Core IO Processing**

* **Tasks:**
    1. **Project Setup:** Initialize FastAPI project, Supabase project, Git repository.
    2. **Core Data Models:** Define and implement Supabase schemas for Publishers, Advertisers, IOs, LineItems, Specs, AI Personas.
    3. **IO Ingestion API:** Develop FastAPI endpoint for IO document upload.
    4. **Basic IO Parsing Agent (LangGraph/LangChain with Pydantic-AI):**
        * Develop an agent to extract key fields from IOs (PDF/text initially). Langflow can be used for prototyping this agent.
        * Store extracted data in Supabase.
    5. **File Storage Logic:** Implement logic to store IOs in Supabase Storage (replicating GDrive structure).
    6. **Basic Workflow (LangGraph):** Orchestrate IO ingestion, parsing, and storage.
* **Team:** Ilija and Uroš (Backend), Pavle (AI Logic & Langflow Prototyping), Vanja (Domain Expert for n8n logic).

#### **Phase 2: Communication & Persona Integration**

* **Tasks:**
    1. **Email Service Integration:** Connect to Google Workspace API.
    2. **Communication Agent (LangGraph/LangChain):**
        * Develop agent to generate emails based on templates and AI Personas (fetched from Supabase). Langflow for prototyping.
        * Initial email: IO received confirmation.
    3. **Workflow Enhancement (LangGraph):** Integrate email sending into the IO processing workflow.
    4. **Deadline Calculation & Basic Reminders:** Replicate n8n logic for deadline calculation and triggering initial reminder emails.
* **Team:** Ilija, Uroš, Pavle, Vanja.

#### **Phase 3: Asset Handling & Basic QA**

* **Tasks:**
    1. **Asset Upload Mechanism:** (Simplest form first, e.g., email reply parsing or dedicated API endpoint for assets).
    2. **File Storage for Assets:** Store assets in Supabase Storage (replicating GDrive structure).
    3. **Asset Validation Agent (LangGraph/LangChain - Basic):**
        * Check file type, dimensions against specs from Supabase.
        * Update asset status in Supabase.
    4. **Workflow Enhancement (LangGraph):** Integrate asset validation and status updates.
    5. **Communication Agent Updates:** Generate emails for asset confirmation/rejection.
* **Team:** Ilija, Uroš, Pavle, Vanja.

#### **Phase 4: Advanced Features, Testing & Refinement**

* **Tasks:**
    1. **Full n8n Logic Replication:** Ensure all nuanced logic from n8n (duplicate IO checks, complex GDrive folder logic, all persona email types) is covered.
    2. **Error Handling & Logging:** Implement robust error handling and logging throughout the system.
    3. **Internal Testing & UAT with Vanja/Product Team.**
    4. **Documentation:** API documentation, basic system documentation.
* **Team:** Ilija, Uroš, Pavle, Vanja (can start looking at API endpoints for future frontend).

*(Frontend development with Next.js/MaterialUI would commence as a subsequent major phase, led by Ilija, once the backend APIs are stable.)*

This System Design Document aims to provide a clear path forward for the development of the YCA Collector v1.
