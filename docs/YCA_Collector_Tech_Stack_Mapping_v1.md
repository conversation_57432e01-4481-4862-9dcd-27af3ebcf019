**YCA Collector v1.0: Tech Stack Mapping**

The objective is to transition the functionality of the existing n8n workflow for creative asset collection to a more robust and scalable system. Here’s a breakdown of the key information and processing flow:

**1\. Request Receipt (IO Document):**

- **Source:** Email with IO attachment (PDF, DOCX, XLSX).

- **System Entry Point:** **FastAPI** endpoint receives the file.

- **Raw File Storage:** The file is immediately stored in **Supabase Storage**.


**2\. IO Document Processing:**

- **Workflow Initiation:** FastAPI triggers a **LangGraph** workflow for IO processing.

- **Text Extraction:** LangGraph invokes a **LangChain Agent (IO Parsing Agent)** which uses Document Loaders to extract text from the IO stored in Supabase Storage.

- **Information Extraction:** The same **LangChain Agent (IO Parsing Agent)** sends the extracted text to an **LLM (Gemini 2.5 Pro)** to extract structured data (Client, Advertiser, Campaign, Line Items, Specifications, Deadlines). Pydantic models ensure data structure.

- **Data Storage:** Extracted data is written to the **Supabase DB (PostgreSQL)**.


**3\. Structure Creation & Initial Communication:**

- **File Organization:** LangGraph workflow (based on data from Supabase DB) ensures the appropriate "folder" structure (path) exists in **Supabase Storage** for the Client, Advertiser, Campaign (for IOs and future creatives).

- **Duplicate Check:** Before finalization, the **Supabase DB** is checked to see if the IO already exists. If so, a notification is sent (e.g., via **Google Chat Webhook**).

- **AI Persona Selection:** LangGraph workflow retrieves the AI persona definition from **Supabase DB**.

- **Sending Initial Email:** LangGraph invokes a **LangChain Agent (Communication Agent)** which, using the selected persona and campaign data from Supabase DB, generates an email via an **LLM**. The email is sent to the agency via **Google Workspace (Gmail API)**.

- **Communication Logging:** The sent email is logged in **Supabase DB**. The status of line items is set to "Pending".


**4\. Creative Asset Receipt:**

- **Source:** Email reply with creatives (JPG, PNG, GIF).

- **System Entry Point:** **FastAPI** endpoint or an email parsing service receives the files.

- **Raw File Storage:** Creatives are stored in **Supabase Storage** at the appropriate path for that campaign/line item.


**5\. Creative Asset Validation:**

- **Workflow Initiation:** A **LangGraph** workflow for creative validation is triggered.

- **Basic Validation:** LangGraph invokes a **LangChain Agent (Asset Validation Agent)** which uses Python libraries (e.g., Pillow) to check dimensions, format, and file size of received creatives (from Supabase Storage) against specifications (from Supabase DB).

- **Status Update:** The status of each creative/line item is updated in **Supabase DB** ("Assets Received" or "Requires Attention").


**6\. Feedback to Agency (Creative Status):**

- **Sending Status Email:** LangGraph invokes a **LangChain Agent (Communication Agent)** that generates an email (using persona and LLM) informing the agency of the status of received creatives (which are OK, which need correction, what's missing). This is sent via **Google Workspace (Gmail API)**.

- **Communication Logging:** The email is logged in **Supabase DB**.


**7\. Follow-up (Automated Reminders):**

- **Trigger:** Periodic task (**FastAPI scheduled task**).

- **Logic:** Checks **Supabase DB** for line items that are "Pending" and where LastEmailDate is more than X business days ago.

- **Sending Reminders:** If conditions are met, it triggers a **LangGraph** workflow that sends a reminder to the agency via the **Communication Agent** and **Gmail API**.


**Key Technologies in the Flow:**

- **API & Initial Processing:** FastAPI

- **Central Workflow Orchestrator:** LangGraph

- **AI Brain (Agents):** LangChain Agents + LLM

- **Database & File Storage:** Supabase

- **Email Communication:** Google Workspace API

- **AI Agent Prototyping (Development Tool):** Langflow
