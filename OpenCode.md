# OpenCode Guidelines

## 1. <PERSON>uild, Lint & Test
- Install dependencies: `uv add package-name`
- Start dev server: `uv run uvicorn app.main:app --reload --port 8000`
- Lint & format: `uv run ruff format .` / `uv run ruff check . --fix`
- Type check: `uv run pyright`
- Run all tests: `uv run pytest`
- Single test file: `uv run pytest tests/test_module.py -v`
- Individual test: `uv run pytest tests/test_module.py::TestClass::test_method`

## 2. Code Style Guidelines
- Imports: group into stdlib, third-party, local with blank lines
- Formatting: max line length 88, autoformat with Ruff
- Type hints: mandatory on all functions, use `Optional` for nullables
- Naming: `snake_case` for funcs/vars, `PascalCase` for classes, `UPPER_SNAKE_CASE` for constants
- Docstrings: required for public APIs and Pydantic models
- Error handling: avoid bare `except`; raise `HTTPException` for API errors
- Config: use `core.config`, do not hardcode secrets
- Logging: structured via `core.utils`, no `print()` in production

## 3. AI Agent Notes
- Read `PLANNING.md` and follow `CLAUDE.md` rules
- Check `TASK.md` for current work and backlog
- Apply any `.cursorrules` or `.github/copilot-instructions.md` if present
