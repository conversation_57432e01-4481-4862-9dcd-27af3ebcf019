# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PYTHONPATH

# UV package manager
.venv/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Pre-commit
.pre-commit-cache/

# Environment variables
.env
.env.local
.env.*.local

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Development and personal files
*.tmp
*.temp
*~
.scratch/
.notes/
.yoyo/
.opencode/

# Personal task and project management files (not TASK.md which is required)
TODO.md
NOTES.md
THOUGHTS.md

# Logs and debugging
*.log
*.log.*
debug.log
error.log
access.log

# Database files
*.sqlite
*.sqlite3
*.db

# SSL certificates and keys
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# Backup files
*.bak
*.backup
*.old
*~

# System files
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE and editor files
*.swp
*.swo
*~
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.idea/workspace.xml
.idea/misc.xml
.idea/modules.xml
.idea/vcs.xml

# Build and deployment artifacts
*.tar.gz
*.zip
*.rar
*.7z

# Docker
.docker/
docker-compose.override.yml
docker-compose.local.yml

# Uploads and storage
uploads/
storage/
files/
assets/temp/

# Cache directories
.cache/
.tmp/
.sass-cache/

.my
.jules

# Task files
# tasks.json
# tasks/

# Multi-agent coordination files (auto-generated, user-specific)
.roomodes
.windsurfrules
.claude/
.agents/

# Cache and auto-generated files
.ruff_cache/
htmlcov/
.coverage
coverage.xml
*.cover

# Development and temporary files
# demo_script.py  # Keep this - it's a useful API demo
*.tmp
*.temp
.scratch/
.notes/

# Editor-specific temporary files
tmp_code_*.bash 