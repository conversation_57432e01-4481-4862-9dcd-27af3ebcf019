
name: "Pre-commit Autoupdate"

on:
  schedule:
    # Weekly on Sundays at midnight UTC
    - cron: '0 0 * * 0'
  workflow_dispatch:

jobs:
  autoupdate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install pre-commit
        run: pip install pre-commit

      - name: Run pre-commit autoupdate
        run: pre-commit autoupdate

      - name: Commit updated hooks
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: chore: pre-commit autoupdate
          branch: main
          file_pattern: .pre-commit-config.yaml
