
name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:

jobs:
  pre-commit:
    name: Run pre-commit hooks
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install uv
          uv sync --dev
      - name: Run pre-commit
        run: uv run pre-commit run --all-files

  lint:
    name: Lint and type-check
    runs-on: ubuntu-latest
    needs: pre-commit
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install uv
          uv sync --dev
      - name: Lint with Ruff
        run: uv run ruff check app/
      - name: Format check with Ruff
        run: uv run ruff format --check app/
      - name: Type-check with Pyright
        run: uv run pyright app/

  test:
    name: Test and coverage
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install uv
          uv sync --dev
      - name: Run tests
        run: uv run pytest app/tests/ --cov=app --cov-report=xml --cov-report=term
      - name: Upload coverage report
        uses: codecov/codecov-action@v3
        with:
          files: coverage.xml
