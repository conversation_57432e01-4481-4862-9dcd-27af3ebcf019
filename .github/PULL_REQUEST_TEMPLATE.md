---
name: Pull Request
about: Describe changes in this PR
title: ''
labels: ''
assignees: ''

---

## Description

Please include a summary of the changes and the related issue.

## Related Issue

Closes # (issue number)

## How Has This Been Tested?

Please describe the tests that you ran to verify your changes.
- [ ] Tests pass locally with `make test` or `uv run pytest`
- [ ] Lint and type-check run with `make lint` or `./scripts/quality-check.sh`
- [ ] Pre-commit hooks pass with `make pre-commit`

## Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented on hard-to-understand areas
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with `make test`
